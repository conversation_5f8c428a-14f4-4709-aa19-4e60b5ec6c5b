import{z as o,bz as c,r as a}from"./index-Dtah4Gl2.js";import{d}from"./detectExtension-EOVHjH6S.js";const t="0x6352211e",n=[{type:"uint256",name:"tokenId"}],r=[{type:"address"}];function i(e){return d({availableSelectors:e,method:[t,n,r]})}function s(e){return o(n,[e.tokenId])}function m(e){return t+s(e).slice(2)}function O(e){return c(r,e)[0]}async function p(e){return a({contract:e.contract,method:[t,n,r],params:[e.tokenId]})}export{t as FN_SELECTOR,O as decodeOwnerOfResult,m as encodeOwnerOf,s as encodeOwnerOfParams,i as isOwnerOfSupported,p as ownerOf};
