const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-nH9aK6xx.js","assets/index-sqJHsJei.css"])))=>i.map(i=>d[i]);
import{dE as c,_ as s,ag as d}from"./index-nH9aK6xx.js";function u(a){return!!a.startsWith("data:application/json;base64")}function l(a){const[,r]=a.split(",");return c(r)}async function h(a){const{client:r,tokenId:n,tokenUri:e}=a;if(u(e))try{return JSON.parse(l(e))}catch(t){throw console.error("Failed to fetch base64 encoded NFT",{tokenId:n,tokenUri:e},t),t}const{download:o}=await s(async()=>{const{download:t}=await import("./index-nH9aK6xx.js").then(i=>i.eI);return{download:t}},__vite__mapDeps([0,1]));try{if(!e.includes("{id}"))return await(await o({client:r,uri:e})).json()}catch(t){throw console.error("Failed to fetch non-dynamic NFT",{tokenId:n,tokenUri:e},t),t}try{try{return await(await o({client:r,uri:e.replace("{id}",d(n,{size:32}).slice(2))})).json()}catch{return await(await o({client:r,uri:e.replace("{id}",n.toString())})).json()}}catch(t){throw console.error("Failed to fetch dynamic NFT",{tokenId:n,tokenUri:e},t),t}}export{h as f};
