import{aC as m,dN as o,dJ as i,dR as y,dS as p,dP as l,dL as v,dT as c,dH as P,dU as w}from"./index-Dtah4Gl2.js";function d(e,r={}){const{recovered:t}=r;if(typeof e.r>"u")throw new s({signature:e});if(typeof e.s>"u")throw new s({signature:e});if(t&&typeof e.yParity>"u")throw new s({signature:e});if(e.r<0n||e.r>y)throw new z({value:e.r});if(e.s<0n||e.s>y)throw new N({value:e.s});if(typeof e.yParity=="number"&&e.yParity!==0&&e.yParity!==1)throw new a({value:e.yParity})}function h(e){return b(v(e))}function b(e){if(e.length!==130&&e.length!==132)throw new $({signature:e});const r=BigInt(c(e,0,32)),t=BigInt(c(e,32,64)),n=(()=>{const u=+`0x${e.slice(130)}`;if(!Number.isNaN(u))try{return f(u)}catch{throw new a({value:u})}})();return typeof n>"u"?{r,s:t}:{r,s:t,yParity:n}}function j(e){if(!(typeof e.r>"u")&&!(typeof e.s>"u"))return x(e)}function x(e){const r=typeof e=="string"?b(e):e instanceof Uint8Array?h(e):typeof e.r=="string"?E(e):e.v?I(e):{r:e.r,s:e.s,...typeof e.yParity<"u"?{yParity:e.yParity}:{}};return d(r),r}function I(e){return{r:e.r,s:e.s,yParity:f(e.v)}}function E(e){const r=(()=>{const t=e.v?Number(e.v):void 0;let n=e.yParity?Number(e.yParity):void 0;if(typeof t=="number"&&typeof n!="number"&&(n=f(t)),typeof n!="number")throw new a({value:e.yParity});return n})();return{r:BigInt(e.r),s:BigInt(e.s),yParity:r}}function O(e){d(e);const r=e.r,t=e.s;return m(o(r,{size:32}),o(t,{size:32}),typeof e.yParity=="number"?o(S(e.yParity),{size:1}):"0x")}function R(e){const{r,s:t,yParity:n}=e;return[n?"0x01":"0x",r===0n?"0x":l(o(r)),t===0n?"0x":l(o(t))]}function f(e){if(e===0||e===27)return 0;if(e===1||e===28)return 1;if(e>=35)return e%2===0?1:0;throw new V({value:e})}function S(e){if(e===0)return 27;if(e===1)return 28;throw new a({value:e})}class $ extends i{constructor({signature:r}){super(`Value \`${r}\` is an invalid signature size.`,{metaMessages:["Expected: 64 bytes or 65 bytes.",`Received ${P(w(r))} bytes.`]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Signature.InvalidSerializedSizeError"})}}class s extends i{constructor({signature:r}){super(`Signature \`${p(r)}\` is missing either an \`r\`, \`s\`, or \`yParity\` property.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Signature.MissingPropertiesError"})}}class z extends i{constructor({value:r}){super(`Value \`${r}\` is an invalid r value. r must be a positive integer less than 2^256.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Signature.InvalidRError"})}}class N extends i{constructor({value:r}){super(`Value \`${r}\` is an invalid s value. s must be a positive integer less than 2^256.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Signature.InvalidSError"})}}class a extends i{constructor({value:r}){super(`Value \`${r}\` is an invalid y-parity value. Y-parity must be 0 or 1.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Signature.InvalidYParityError"})}}class V extends i{constructor({value:r}){super(`Value \`${r}\` is an invalid v value. v must be 27, 28 or >=35.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Signature.InvalidVError"})}}export{V as I,O as a,j as e,I as f,R as t,f as v,S as y};
