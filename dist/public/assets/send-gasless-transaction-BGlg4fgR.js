const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/biconomy-C6fqMFoB.js","assets/index-nH9aK6xx.js","assets/index-sqJHsJei.css","assets/openzeppelin-C_S7ZEcT.js","assets/engine-88nOnU9E.js"])))=>i.map(i=>d[i]);
import{_ as p,at as d}from"./index-nH9aK6xx.js";async function s({account:e,transaction:i,serializableTransaction:a,gasless:n}){if(a.value&&a.value>0n)throw new Error("Gasless transactions cannot have a value");let r;if(n.provider==="biconomy"){const{relayBiconomyTransaction:t}=await p(async()=>{const{relayBiconomyTransaction:o}=await import("./biconomy-C6fqMFoB.js");return{relayBiconomyTransaction:o}},__vite__mapDeps([0,1,2]));r=await t({account:e,transaction:i,serializableTransaction:a,gasless:n})}if(n.provider==="openzeppelin"){const{relayOpenZeppelinTransaction:t}=await p(async()=>{const{relayOpenZeppelinTransaction:o}=await import("./openzeppelin-C_S7ZEcT.js");return{relayOpenZeppelinTransaction:o}},__vite__mapDeps([3,1,2]));r=await t({account:e,transaction:i,serializableTransaction:a,gasless:n})}if(n.provider==="engine"){const{relayEngineTransaction:t}=await p(async()=>{const{relayEngineTransaction:o}=await import("./engine-88nOnU9E.js");return{relayEngineTransaction:o}},__vite__mapDeps([4,1,2]));r=await t({account:e,transaction:i,serializableTransaction:a,gasless:n})}if(!r)throw new Error("Unsupported gasless provider");return d({address:e.address,transactionHash:r.transactionHash,chainId:i.chain.id}),r}export{s as sendGaslessTransaction};
