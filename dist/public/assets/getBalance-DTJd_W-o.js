import{r as c,aA as r,aB as n}from"./index-nH9aK6xx.js";const s="0x70a08231",d=[{type:"address",name:"_address"}],o=[{type:"uint256"}];async function l(a){return c({contract:a.contract,method:[s,d,o],params:[a.address]})}async function i(a){const[e,t]=await Promise.all([l(a),r(a)]);return{...t,value:e,displayValue:n(e,t.decimals),tokenAddress:a.contract.address,chainId:a.contract.chain.id}}export{i as getBalance};
