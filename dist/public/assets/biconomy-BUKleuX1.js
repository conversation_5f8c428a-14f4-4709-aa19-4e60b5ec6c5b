import{f as c,b as p,F as u,r as f,Z as m,bg as g,M as i,y as l}from"./index-Dtah4Gl2.js";const d=0n;async function x({account:a,serializableTransaction:e,transaction:s,gasless:n}){const r=u({address:n.relayer<PERSON><PERSON><PERSON><PERSON>Address,chain:s.chain,client:s.client}),o=await f({contract:r,method:"function getNonce(address,uint256) view returns (uint256)",params:[a.address,d]}),h=Math.floor(Date.now()/1e3)+(n.deadlineSeconds??3600),t={from:a.address,to:e.to,token:m,txGas:e.gas,tokenGasPrice:0n,batchId:d,batchNonce:o,deadline:h,data:e.data};if(!t.to)throw new Error("Cannot send a transaction without a `to` address");if(!t.txGas)throw new Error("Cannot send a transaction without a `gas` value");if(!t.data)throw new Error("Cannot send a transaction without a `data` value");const w=g([{type:"address"},{type:"address"},{type:"address"},{type:"uint256"},{type:"uint256"},{type:"uint256"},{type:"uint256"},{type:"bytes32"}],[i(t.from),i(t.to),i(t.token),t.txGas,t.tokenGasPrice,t.batchId,t.batchNonce,l(t.data)]),y=await a.signMessage({message:w});return[t,y]}async function k(a){const[e,s]=await x(a),n=await fetch("https://api.biconomy.io/api/v2/meta-tx/native",{method:"POST",body:c({apiId:a.gasless.apiId,params:[e,s],from:e.from,to:e.to,gasLimit:e.txGas}),headers:{"x-api-key":a.gasless.apiKey,"Content-Type":"application/json;charset=utf-8"}});if(!n.ok)throw new Error(`Failed to send transaction: ${await n.text()}`);const r=await n.json(),o=r.txHash;if(p(o))return{transactionHash:o,chain:a.transaction.chain,client:a.transaction.client};throw new Error(`Failed to send transaction: ${c(r)}`)}export{x as prepareBiconomyTransaction,k as relayBiconomyTransaction};
