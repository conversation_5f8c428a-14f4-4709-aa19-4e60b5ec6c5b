import{b0 as Rt,b1 as Mt,b2 as Js,b3 as Xs,b4 as Ho,b5 as Wr,b6 as Uo,b7 as Vo,b8 as Wo,b9 as xt,ba as ut,bb as M,bc as Ke,bd as We,be as dr,bf as dt,bg as Ye,bh as ue,aF as X,aq as k,bi as Qs,bj as ea,bk as Zo,bl as fr,bm as ve,bn as ta,aE as G,e as _,_ as lr,bo as j,bp as ft,bq as V,as as ce,br as pe,bs as me,bt as Zr,bu as na,a2 as ne,aD as zt,bv as _e,bw as Ie,bx as pr,by as ra,bz as Ht,bA as sa,bB as qt,bC as aa,bD as oa,bE as ia,bF as ca,bG as mr,bH as Ko,bI as ot,bJ as _t,bK as Yo,bL as Kr,bM as <PERSON>,bN as Xo,bO as Qo,bP as ei,ar as Te,bQ as Ct,c as Ut,bR as ti,bS as Ze,bT as Oe,bU as ni,d as Bt,bV as ri,bW as si,bX as ai,bY as oi,bZ as ii,b_ as ci,b$ as Ee,c0 as ui,c1 as ua,c2 as da,c3 as Yr,c4 as di,c5 as fi,c6 as li,c7 as Jr,c8 as Bn,c9 as pi,ca as mi,cb as hi,cc as Xr,cd as yi,ce as Vt,cf as fa,cg as Ot,ch as la,ci as Ne,cj as Wt,ck as bi,cl as it,cm as pa,cn as kn,co as Ve,cp as ie,cq as Qr,cr as gi,cs as $n,ct as vt,cu as Nn,cv as es,cw as wi,cx as xi,ay as vi,cy as Ei,al as Pi,am as Ti,an as Ii}from"./index-Dtah4Gl2.js";import{cL as T0,cM as I0,cN as A0,cO as S0,cP as C0,cQ as B0,cR as k0,cz as $0,c_ as N0,cX as F0,cS as R0,cT as M0,cA as z0,cB as q0,cW as _0,cU as O0,d1 as L0,cC as G0,cY as j0,cD as D0,cE as H0,cF as U0,d2 as V0,cG as W0,cH as Z0,d3 as K0,c$ as Y0,cZ as J0,d0 as X0,cI as Q0,cK as ep,cJ as tp,dc as np,de as rp,df as sp,dg as ap,dh as op,dj as ip,d4 as cp,dk as up,d5 as dp,d7 as fp,d8 as lp,dt as pp,di as mp,dl as hp,dn as yp,dp as bp,dp as gp,da as wp,db as xp,dm as vp,dd as Ep,dr as Pp,d9 as Tp,cV as Ip,ds as Ap,dq as Sp,dn as Cp,dp as Bp,dq as kp,dp as $p,d6 as Np}from"./index-Dtah4Gl2.js";import{g as Ai,I as Lt,R as hr,a as Gt,M as Et,H as Fe,b as Si,U as kt,A as ts,c as ns,B as rs,d as ss,D as as,e as os,f as is,S as cs,C as us,P as ds,h as fs,i as ls,J as ps,L as Fn,T as Rn,j as ms,k as hs,l as ys,m as bs,n as gs,o as ws,p as Mn,q as ma,r as jt,W as xs}from"./rpc-BcJ5ThoN.js";import{s as Rp,t as Mp}from"./rpc-BcJ5ThoN.js";import{t as Ae,a as Zt,s as Ci}from"./sha256-Cy_8yqXb.js";import{aI as qp,aJ as _p,o as Op,p as Lp,q as Gp,r as jp,u as Dp,v as Hp,w as Up,c as Vp,x as Wp,y as Zp,z as Kp,A as Yp,B as Jp,C as Xp,D as Qp,E as em,F as tm,G as nm,d as rm,H as sm,I as am,J as om,e as im,f as cm,g as um,h as dm,i as fm,j as lm,b as pm,k as mm,l as hm,n as ym,V as bm,W as gm,X as wm,Y as xm,Z as vm,_ as Em,$ as Pm,m as Tm,a0 as Im,a1 as Am,a2 as Sm,a3 as Cm,a4 as Bm,a5 as km,a6 as $m,a7 as Nm,a8 as Fm,a9 as Rm,L as Mm,aa as zm,ab as qm,M as _m,N as Om,O as Lm,P as Gm,Q as jm,R as Dm,K as Hm,S as Um,T as Vm,U as Wm,ao as Zm,ap as Km,aq as Ym,ar as Jm,as as Xm,at as Qm,au as eh,ad as th,av as nh,aw as rh,ax as sh,ay as ah,az as oh,aA as ih,aB as ch,aC as uh,aD as dh,aE as fh,ae as lh,aF as ph,aG as mh,aH as hh,af as yh,ag as bh,ah as gh,ai as wh,aj as xh,ak as vh,ac as Eh,al as Ph,am as Th,an as Ih}from"./sha256-Cy_8yqXb.js";function Rl(e){let t;if(typeof e=="string")t=Rt(e,{modifiers:Mt});else{const n=Js(e),r=e.length;for(let s=0;s<r;s++){const a=e[s];if(!Xs(a)){t=Rt(a,{modifiers:Mt,structs:n});break}}}if(!t)throw new Ho({param:e});return t}function Ml(e){const t=[];if(typeof e=="string"){const n=Wr(e),r=n.length;for(let s=0;s<r;s++)t.push(Rt(n[s],{modifiers:Mt}))}else{const n=Js(e),r=e.length;for(let s=0;s<r;s++){const a=e[s];if(Xs(a))continue;const o=Wr(a),c=o.length;for(let i=0;i<c;i++)t.push(Rt(o[i],{modifiers:Mt,structs:n}))}}if(t.length===0)throw new Uo({params:e});return t}const Bi=new Uint8Array([7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8]),ha=new Uint8Array(new Array(16).fill(0).map((e,t)=>t)),ki=ha.map(e=>(9*e+5)%16);let yr=[ha],br=[ki];for(let e=0;e<4;e++)for(let t of[yr,br])t.push(t[e].map(n=>Bi[n]));const ya=[[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8],[12,13,11,15,6,9,9,7,12,15,11,13,7,8,7,7],[13,15,14,11,7,7,6,8,13,14,13,12,5,5,6,9],[14,11,12,14,8,6,5,5,15,12,15,14,9,9,8,6],[15,12,13,13,9,5,8,6,14,11,12,11,8,6,5,5]].map(e=>new Uint8Array(e)),$i=yr.map((e,t)=>e.map(n=>ya[t][n])),Ni=br.map((e,t)=>e.map(n=>ya[t][n])),Fi=new Uint32Array([0,1518500249,1859775393,2400959708,2840853838]),Ri=new Uint32Array([1352829926,1548603684,1836072691,2053994217,0]);function vs(e,t,n,r){return e===0?t^n^r:e===1?t&n|~t&r:e===2?(t|~n)^r:e===3?t&r|n&~r:t^(n|~r)}const Pt=new Uint32Array(16);class Mi extends Wo{constructor(){super(64,20,8,!0),this.h0=1732584193,this.h1=-271733879,this.h2=-1732584194,this.h3=271733878,this.h4=-1009589776}get(){const{h0:t,h1:n,h2:r,h3:s,h4:a}=this;return[t,n,r,s,a]}set(t,n,r,s,a){this.h0=t|0,this.h1=n|0,this.h2=r|0,this.h3=s|0,this.h4=a|0}process(t,n){for(let p=0;p<16;p++,n+=4)Pt[p]=t.getUint32(n,!0);let r=this.h0|0,s=r,a=this.h1|0,o=a,c=this.h2|0,i=c,u=this.h3|0,f=u,d=this.h4|0,l=d;for(let p=0;p<5;p++){const y=4-p,h=Fi[p],m=Ri[p],b=yr[p],g=br[p],w=$i[p],P=Ni[p];for(let E=0;E<16;E++){const x=xt(r+vs(p,a,c,u)+Pt[b[E]]+h,w[E])+d|0;r=d,d=u,u=xt(c,10)|0,c=a,a=x}for(let E=0;E<16;E++){const x=xt(s+vs(y,o,i,f)+Pt[g[E]]+m,P[E])+l|0;s=l,l=f,f=xt(i,10)|0,i=o,o=x}}this.set(this.h1+c+f|0,this.h2+u+l|0,this.h3+d+s|0,this.h4+r+o|0,this.h0+a+i|0)}roundClean(){Pt.fill(0)}destroy(){this.destroyed=!0,this.buffer.fill(0),this.set(0,0,0,0,0)}}const zi=Vo(()=>new Mi);function F(e,t,n){const r=e[t.name];if(typeof r=="function")return r;const s=e[n];return typeof s=="function"?s:a=>t(e,a)}function Kt(e,{method:t}){var r,s;const n={};return e.transport.type==="fallback"&&((s=(r=e.transport).onResponse)==null||s.call(r,({method:a,response:o,status:c,transport:i})=>{c==="success"&&t===a&&(n[o]=i.request)})),a=>n[a]||e.request}async function gr(e,t){const{address:n,abi:r,args:s,eventName:a,fromBlock:o,strict:c,toBlock:i}=t,u=Kt(e,{method:"eth_newFilter"}),f=a?ut({abi:r,args:s,eventName:a}):void 0,d=await e.request({method:"eth_newFilter",params:[{address:n,fromBlock:typeof o=="bigint"?M(o):o,toBlock:typeof i=="bigint"?M(i):i,topics:f}]});return{abi:r,args:s,eventName:a,id:d,request:u(d),strict:!!c,type:"event"}}function W(e){return typeof e=="string"?{address:e,type:"json-rpc"}:e}const Es="/docs/contract/encodeFunctionData";function qi(e){const{abi:t,args:n,functionName:r}=e;let s=t[0];if(r){const a=Ke({abi:t,args:n,name:r});if(!a)throw new We(r,{docsPath:Es});s=a}if(s.type!=="function")throw new We(void 0,{docsPath:Es});return{abi:[s],functionName:dr(dt(s))}}function oe(e){const{args:t}=e,{abi:n,functionName:r}=(()=>{var c;return e.abi.length===1&&((c=e.functionName)!=null&&c.startsWith("0x"))?e:qi(e)})(),s=n[0],a=r,o="inputs"in s&&s.inputs?Ye(s.inputs,t??[]):void 0;return ue([a,o??"0x"])}function ba({abiItem:e,args:t,includeFunctionName:n=!0,includeName:r=!1}){if("name"in e&&"inputs"in e&&e.inputs)return`${n?e.name:""}(${e.inputs.map((s,a)=>`${r&&s.name?`${s.name}: `:""}${typeof t[a]=="object"?X(t[a]):t[a]}`).join(", ")})`}const ga={gwei:9,wei:18},wa={ether:-9,wei:9},zl={ether:-18,gwei:-9};function xa(e,t){let n=e.toString();const r=n.startsWith("-");r&&(n=n.slice(1)),n=n.padStart(t,"0");let[s,a]=[n.slice(0,n.length-t),n.slice(n.length-t)];return a=a.replace(/(0+)$/,""),`${r?"-":""}${s||"0"}${a?`.${a}`:""}`}function wr(e,t="wei"){return xa(e,ga[t])}function te(e,t="wei"){return xa(e,wa[t])}class _i extends k{constructor({address:t}){super(`State for account "${t}" is set multiple times.`,{name:"AccountStateConflictError"})}}class Oi extends k{constructor(){super("state and stateDiff are set on the same account.",{name:"StateAssignmentConflictError"})}}function Ps(e){return e.reduce((t,{slot:n,value:r})=>`${t}        ${n}: ${r}
`,"")}function Li(e){return e.reduce((t,{address:n,...r})=>{let s=`${t}    ${n}:
`;return r.nonce&&(s+=`      nonce: ${r.nonce}
`),r.balance&&(s+=`      balance: ${r.balance}
`),r.code&&(s+=`      code: ${r.code}
`),r.state&&(s+=`      state:
`,s+=Ps(r.state)),r.stateDiff&&(s+=`      stateDiff:
`,s+=Ps(r.stateDiff)),s},`  State Override:
`).slice(0,-1)}function lt(e){const t=Object.entries(e).map(([r,s])=>s===void 0||s===!1?null:[r,s]).filter(Boolean),n=t.reduce((r,[s])=>Math.max(r,s.length),0);return t.map(([r,s])=>`  ${`${r}:`.padEnd(n+1)}  ${s}`).join(`
`)}class Gi extends k{constructor(){super(["Cannot specify both a `gasPrice` and a `maxFeePerGas`/`maxPriorityFeePerGas`.","Use `maxFeePerGas`/`maxPriorityFeePerGas` for EIP-1559 compatible networks, and `gasPrice` for others."].join(`
`),{name:"FeeConflictError"})}}class va extends k{constructor({v:t}){super(`Invalid \`v\` value "${t}". Expected 27 or 28.`,{name:"InvalidLegacyVError"})}}class ji extends k{constructor({transaction:t}){super("Cannot infer a transaction type from provided transaction.",{metaMessages:["Provided Transaction:","{",lt(t),"}","","To infer the type, either provide:","- a `type` to the Transaction, or","- an EIP-1559 Transaction with `maxFeePerGas`, or","- an EIP-2930 Transaction with `gasPrice` & `accessList`, or","- an EIP-4844 Transaction with `blobs`, `blobVersionedHashes`, `sidecars`, or","- an EIP-7702 Transaction with `authorizationList`, or","- a Legacy Transaction with `gasPrice`"],name:"InvalidSerializableTransactionError"})}}class Di extends k{constructor({serializedType:t}){super(`Serialized transaction type "${t}" is invalid.`,{name:"InvalidSerializedTransactionType"}),Object.defineProperty(this,"serializedType",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.serializedType=t}}class pt extends k{constructor({attributes:t,serializedTransaction:n,type:r}){const s=Object.entries(t).map(([a,o])=>typeof o>"u"?a:void 0).filter(Boolean);super(`Invalid serialized transaction of type "${r}" was provided.`,{metaMessages:[`Serialized Transaction: "${n}"`,s.length>0?`Missing Attributes: ${s.join(", ")}`:""].filter(Boolean),name:"InvalidSerializedTransactionError"}),Object.defineProperty(this,"serializedTransaction",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"type",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.serializedTransaction=n,this.type=r}}class Hi extends k{constructor({storageKey:t}){super(`Size for storage key "${t}" is invalid. Expected 32 bytes. Got ${Math.floor((t.length-2)/2)} bytes.`,{name:"InvalidStorageKeySizeError"})}}class Ui extends k{constructor(t,{account:n,docsPath:r,chain:s,data:a,gas:o,gasPrice:c,maxFeePerGas:i,maxPriorityFeePerGas:u,nonce:f,to:d,value:l}){var y;const p=lt({chain:s&&`${s==null?void 0:s.name} (id: ${s==null?void 0:s.id})`,from:n==null?void 0:n.address,to:d,value:typeof l<"u"&&`${wr(l)} ${((y=s==null?void 0:s.nativeCurrency)==null?void 0:y.symbol)||"ETH"}`,data:a,gas:o,gasPrice:typeof c<"u"&&`${te(c)} gwei`,maxFeePerGas:typeof i<"u"&&`${te(i)} gwei`,maxPriorityFeePerGas:typeof u<"u"&&`${te(u)} gwei`,nonce:f});super(t.shortMessage,{cause:t,docsPath:r,metaMessages:[...t.metaMessages?[...t.metaMessages," "]:[],"Request Arguments:",p].filter(Boolean),name:"TransactionExecutionError"}),Object.defineProperty(this,"cause",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.cause=t}}class Ea extends k{constructor({blockHash:t,blockNumber:n,blockTag:r,hash:s,index:a}){let o="Transaction";r&&a!==void 0&&(o=`Transaction at block time "${r}" at index "${a}"`),t&&a!==void 0&&(o=`Transaction at block hash "${t}" at index "${a}"`),n&&a!==void 0&&(o=`Transaction at block number "${n}" at index "${a}"`),s&&(o=`Transaction with hash "${s}"`),super(`${o} could not be found.`,{name:"TransactionNotFoundError"})}}class Pa extends k{constructor({hash:t}){super(`Transaction receipt with hash "${t}" could not be found. The Transaction may not be processed on a block yet.`,{name:"TransactionReceiptNotFoundError"})}}class Vi extends k{constructor({hash:t}){super(`Timed out while waiting for transaction with hash "${t}" to be confirmed.`,{name:"WaitForTransactionReceiptTimeoutError"})}}class Ta extends k{constructor(t,{account:n,docsPath:r,chain:s,data:a,gas:o,gasPrice:c,maxFeePerGas:i,maxPriorityFeePerGas:u,nonce:f,to:d,value:l,stateOverride:p}){var m;const y=n?W(n):void 0;let h=lt({from:y==null?void 0:y.address,to:d,value:typeof l<"u"&&`${wr(l)} ${((m=s==null?void 0:s.nativeCurrency)==null?void 0:m.symbol)||"ETH"}`,data:a,gas:o,gasPrice:typeof c<"u"&&`${te(c)} gwei`,maxFeePerGas:typeof i<"u"&&`${te(i)} gwei`,maxPriorityFeePerGas:typeof u<"u"&&`${te(u)} gwei`,nonce:f});p&&(h+=`
${Li(p)}`),super(t.shortMessage,{cause:t,docsPath:r,metaMessages:[...t.metaMessages?[...t.metaMessages," "]:[],"Raw Call Arguments:",h].filter(Boolean),name:"CallExecutionError"}),Object.defineProperty(this,"cause",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.cause=t}}class Wi extends k{constructor(t,{abi:n,args:r,contractAddress:s,docsPath:a,functionName:o,sender:c}){const i=Ke({abi:n,args:r,name:o}),u=i?ba({abiItem:i,args:r,includeFunctionName:!1,includeName:!1}):void 0,f=i?dt(i,{includeName:!0}):void 0,d=lt({address:s&&Ai(s),function:f,args:u&&u!=="()"&&`${[...Array((o==null?void 0:o.length)??0).keys()].map(()=>" ").join("")}${u}`,sender:c});super(t.shortMessage||`An unknown error occurred while executing the contract function "${o}".`,{cause:t,docsPath:a,metaMessages:[...t.metaMessages?[...t.metaMessages," "]:[],d&&"Contract Call:",d].filter(Boolean),name:"ContractFunctionExecutionError"}),Object.defineProperty(this,"abi",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"args",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"cause",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"contractAddress",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"formattedArgs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"functionName",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"sender",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.abi=n,this.args=r,this.cause=t,this.contractAddress=s,this.functionName=o,this.sender=c}}class zn extends k{constructor({abi:t,data:n,functionName:r,message:s}){let a,o,c,i;if(n&&n!=="0x")try{o=Qs({abi:t,data:n});const{abiItem:f,errorName:d,args:l}=o;if(d==="Error")i=l[0];else if(d==="Panic"){const[p]=l;i=ea[p]}else{const p=f?dt(f,{includeName:!0}):void 0,y=f&&l?ba({abiItem:f,args:l,includeFunctionName:!1,includeName:!1}):void 0;c=[p?`Error: ${p}`:"",y&&y!=="()"?`       ${[...Array((d==null?void 0:d.length)??0).keys()].map(()=>" ").join("")}${y}`:""]}}catch(f){a=f}else s&&(i=s);let u;a instanceof Zo&&(u=a.signature,c=[`Unable to decode signature "${u}" as it was not found on the provided ABI.`,"Make sure you are using the correct ABI and that the error exists on it.",`You can look up the decoded signature here: https://openchain.xyz/signatures?query=${u}.`]),super(i&&i!=="execution reverted"||u?[`The contract function "${r}" reverted with the following ${u?"signature":"reason"}:`,i||u].join(`
`):`The contract function "${r}" reverted.`,{cause:a,metaMessages:c,name:"ContractFunctionRevertedError"}),Object.defineProperty(this,"data",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"raw",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"reason",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"signature",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.data=o,this.raw=n,this.reason=i,this.signature=u}}class Zi extends k{constructor({functionName:t}){super(`The contract function "${t}" returned no data ("0x").`,{metaMessages:["This could be due to any of the following:",`  - The contract does not have the function "${t}",`,"  - The parameters passed to the contract function may be invalid, or","  - The address is not a contract."],name:"ContractFunctionZeroDataError"})}}class Ki extends k{constructor({factory:t}){super(`Deployment for counterfactual contract call failed${t?` for factory "${t}".`:""}`,{metaMessages:["Please ensure:","- The `factory` is a valid contract deployment factory (ie. Create2 Factory, ERC-4337 Factory, etc).","- The `factoryData` is a valid encoded function call for contract deployment function on the factory."],name:"CounterfactualDeploymentFailedError"})}}class Yt extends k{constructor({data:t,message:n}){super(n||"",{name:"RawContractError"}),Object.defineProperty(this,"code",{enumerable:!0,configurable:!0,writable:!0,value:3}),Object.defineProperty(this,"data",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.data=t}}const Yi=3;function Re(e,{abi:t,address:n,args:r,docsPath:s,functionName:a,sender:o}){const c=e instanceof Yt?e:e instanceof k?e.walk(y=>"data"in y)||e.walk():{},{code:i,data:u,details:f,message:d,shortMessage:l}=c,p=e instanceof fr?new Zi({functionName:a}):[Yi,Lt.code].includes(i)&&(u||f||d||l)?new zn({abi:t,data:typeof u=="object"?u.data:u,functionName:a,message:c instanceof hr?f:l??d}):e;return new Wi(p,{abi:t,args:r,contractAddress:n,docsPath:s,functionName:a,sender:o})}function Ji(e){const t=ve(`0x${e.substring(4)}`).substring(26);return ta(`0x${t}`)}async function Xi({hash:e,signature:t}){const n=G(e)?e:_(e),{secp256k1:r}=await lr(async()=>{const{secp256k1:o}=await Promise.resolve().then(()=>Pf);return{secp256k1:o}},void 0);return`0x${(()=>{if(typeof t=="object"&&"r"in t&&"s"in t){const{r:u,s:f,v:d,yParity:l}=t,p=Number(l??d),y=Ts(p);return new r.Signature(j(u),j(f)).addRecoveryBit(y)}const o=G(t)?t:_(t);if(ft(o)!==65)throw new Error("invalid signature length");const c=V(`0x${o.slice(130)}`),i=Ts(c);return r.Signature.fromCompact(o.substring(2,130)).addRecoveryBit(i)})().recoverPublicKey(n.substring(2)).toHex(!1)}`}function Ts(e){if(e===0||e===1)return e;if(e===27)return 0;if(e===28)return 1;throw new Error("Invalid yParityOrV value")}async function Je({hash:e,signature:t}){return Ji(await Xi({hash:e,signature:t}))}function Qi(e){const{chainId:t,nonce:n,to:r}=e,s=e.contractAddress??e.address,a=ve(ue(["0x05",Ae([t?M(t):"0x",s,n?M(n):"0x"])]));return r==="bytes"?ce(a):a}async function Ia(e){const{authorization:t,signature:n}=e;return Je({hash:Qi(t),signature:n??t})}class ec extends k{constructor(t,{account:n,docsPath:r,chain:s,data:a,gas:o,gasPrice:c,maxFeePerGas:i,maxPriorityFeePerGas:u,nonce:f,to:d,value:l}){var y;const p=lt({from:n==null?void 0:n.address,to:d,value:typeof l<"u"&&`${wr(l)} ${((y=s==null?void 0:s.nativeCurrency)==null?void 0:y.symbol)||"ETH"}`,data:a,gas:o,gasPrice:typeof c<"u"&&`${te(c)} gwei`,maxFeePerGas:typeof i<"u"&&`${te(i)} gwei`,maxPriorityFeePerGas:typeof u<"u"&&`${te(u)} gwei`,nonce:f});super(t.shortMessage,{cause:t,docsPath:r,metaMessages:[...t.metaMessages?[...t.metaMessages," "]:[],"Estimate Gas Arguments:",p].filter(Boolean),name:"EstimateGasExecutionError"}),Object.defineProperty(this,"cause",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.cause=t}}class $e extends k{constructor({cause:t,message:n}={}){var s;const r=(s=n==null?void 0:n.replace("execution reverted: ",""))==null?void 0:s.replace("execution reverted","");super(`Execution reverted ${r?`with reason: ${r}`:"for an unknown reason"}.`,{cause:t,name:"ExecutionRevertedError"})}}Object.defineProperty($e,"code",{enumerable:!0,configurable:!0,writable:!0,value:3});Object.defineProperty($e,"nodeMessage",{enumerable:!0,configurable:!0,writable:!0,value:/execution reverted/});class Me extends k{constructor({cause:t,maxFeePerGas:n}={}){super(`The fee cap (\`maxFeePerGas\`${n?` = ${te(n)} gwei`:""}) cannot be higher than the maximum allowed value (2^256-1).`,{cause:t,name:"FeeCapTooHighError"})}}Object.defineProperty(Me,"nodeMessage",{enumerable:!0,configurable:!0,writable:!0,value:/max fee per gas higher than 2\^256-1|fee cap higher than 2\^256-1/});class qn extends k{constructor({cause:t,maxFeePerGas:n}={}){super(`The fee cap (\`maxFeePerGas\`${n?` = ${te(n)}`:""} gwei) cannot be lower than the block base fee.`,{cause:t,name:"FeeCapTooLowError"})}}Object.defineProperty(qn,"nodeMessage",{enumerable:!0,configurable:!0,writable:!0,value:/max fee per gas less than block base fee|fee cap less than block base fee|transaction is outdated/});class _n extends k{constructor({cause:t,nonce:n}={}){super(`Nonce provided for the transaction ${n?`(${n}) `:""}is higher than the next one expected.`,{cause:t,name:"NonceTooHighError"})}}Object.defineProperty(_n,"nodeMessage",{enumerable:!0,configurable:!0,writable:!0,value:/nonce too high/});class On extends k{constructor({cause:t,nonce:n}={}){super([`Nonce provided for the transaction ${n?`(${n}) `:""}is lower than the current nonce of the account.`,"Try increasing the nonce or find the latest nonce with `getTransactionCount`."].join(`
`),{cause:t,name:"NonceTooLowError"})}}Object.defineProperty(On,"nodeMessage",{enumerable:!0,configurable:!0,writable:!0,value:/nonce too low|transaction already imported|already known/});class Ln extends k{constructor({cause:t,nonce:n}={}){super(`Nonce provided for the transaction ${n?`(${n}) `:""}exceeds the maximum allowed nonce.`,{cause:t,name:"NonceMaxValueError"})}}Object.defineProperty(Ln,"nodeMessage",{enumerable:!0,configurable:!0,writable:!0,value:/nonce has max value/});class Gn extends k{constructor({cause:t}={}){super(["The total cost (gas * gas fee + value) of executing this transaction exceeds the balance of the account."].join(`
`),{cause:t,metaMessages:["This error could arise when the account does not have enough funds to:"," - pay for the total gas fee,"," - pay for the value to send."," ","The cost of the transaction is calculated as `gas * gas fee + value`, where:"," - `gas` is the amount of gas needed for transaction to execute,"," - `gas fee` is the gas fee,"," - `value` is the amount of ether to send to the recipient."],name:"InsufficientFundsError"})}}Object.defineProperty(Gn,"nodeMessage",{enumerable:!0,configurable:!0,writable:!0,value:/insufficient funds|exceeds transaction sender account balance/});class jn extends k{constructor({cause:t,gas:n}={}){super(`The amount of gas ${n?`(${n}) `:""}provided for the transaction exceeds the limit allowed for the block.`,{cause:t,name:"IntrinsicGasTooHighError"})}}Object.defineProperty(jn,"nodeMessage",{enumerable:!0,configurable:!0,writable:!0,value:/intrinsic gas too high|gas limit reached/});class Dn extends k{constructor({cause:t,gas:n}={}){super(`The amount of gas ${n?`(${n}) `:""}provided for the transaction is too low.`,{cause:t,name:"IntrinsicGasTooLowError"})}}Object.defineProperty(Dn,"nodeMessage",{enumerable:!0,configurable:!0,writable:!0,value:/intrinsic gas too low/});class Hn extends k{constructor({cause:t}){super("The transaction type is not supported for this chain.",{cause:t,name:"TransactionTypeNotSupportedError"})}}Object.defineProperty(Hn,"nodeMessage",{enumerable:!0,configurable:!0,writable:!0,value:/transaction type not valid/});class ct extends k{constructor({cause:t,maxPriorityFeePerGas:n,maxFeePerGas:r}={}){super([`The provided tip (\`maxPriorityFeePerGas\`${n?` = ${te(n)} gwei`:""}) cannot be higher than the fee cap (\`maxFeePerGas\`${r?` = ${te(r)} gwei`:""}).`].join(`
`),{cause:t,name:"TipAboveFeeCapError"})}}Object.defineProperty(ct,"nodeMessage",{enumerable:!0,configurable:!0,writable:!0,value:/max priority fee per gas higher than max fee per gas|tip higher than fee cap/});class mt extends k{constructor({cause:t}){super(`An error occurred while executing: ${t==null?void 0:t.shortMessage}`,{cause:t,name:"UnknownNodeError"})}}function Jt(e,t){const n=(e.details||"").toLowerCase(),r=e instanceof k?e.walk(s=>(s==null?void 0:s.code)===$e.code):e;return r instanceof k?new $e({cause:e,message:r.details}):$e.nodeMessage.test(n)?new $e({cause:e,message:e.details}):Me.nodeMessage.test(n)?new Me({cause:e,maxFeePerGas:t==null?void 0:t.maxFeePerGas}):qn.nodeMessage.test(n)?new qn({cause:e,maxFeePerGas:t==null?void 0:t.maxFeePerGas}):_n.nodeMessage.test(n)?new _n({cause:e,nonce:t==null?void 0:t.nonce}):On.nodeMessage.test(n)?new On({cause:e,nonce:t==null?void 0:t.nonce}):Ln.nodeMessage.test(n)?new Ln({cause:e,nonce:t==null?void 0:t.nonce}):Gn.nodeMessage.test(n)?new Gn({cause:e}):jn.nodeMessage.test(n)?new jn({cause:e,gas:t==null?void 0:t.gas}):Dn.nodeMessage.test(n)?new Dn({cause:e,gas:t==null?void 0:t.gas}):Hn.nodeMessage.test(n)?new Hn({cause:e}):ct.nodeMessage.test(n)?new ct({cause:e,maxFeePerGas:t==null?void 0:t.maxFeePerGas,maxPriorityFeePerGas:t==null?void 0:t.maxPriorityFeePerGas}):new mt({cause:e})}function tc(e,{docsPath:t,...n}){const r=(()=>{const s=Jt(e,n);return s instanceof mt?e:s})();return new ec(r,{docsPath:t,...n})}function ht(e,{format:t}){if(!t)return{};const n={};function r(a){const o=Object.keys(a);for(const c of o)c in e&&(n[c]=e[c]),a[c]&&typeof a[c]=="object"&&!Array.isArray(a[c])&&r(a[c])}const s=t(e||{});return r(s),n}function Is(e){if(!(!e||e.length===0))return e.reduce((t,{slot:n,value:r})=>{if(n.length!==66)throw new Zr({size:n.length,targetSize:66,type:"hex"});if(r.length!==66)throw new Zr({size:r.length,targetSize:66,type:"hex"});return t[n]=r,t},{})}function nc(e){const{balance:t,nonce:n,state:r,stateDiff:s,code:a}=e,o={};if(a!==void 0&&(o.code=a),t!==void 0&&(o.balance=M(t)),n!==void 0&&(o.nonce=M(n)),r!==void 0&&(o.state=Is(r)),s!==void 0){if(o.state)throw new Oi;o.stateDiff=Is(s)}return o}function xr(e){if(!e)return;const t={};for(const{address:n,...r}of e){if(!pe(n,{strict:!1}))throw new me({address:n});if(t[n])throw new _i({address:n});t[n]=nc(r)}return t}function Le(e){const{account:t,gasPrice:n,maxFeePerGas:r,maxPriorityFeePerGas:s,to:a}=e,o=t?W(t):void 0;if(o&&!pe(o.address))throw new me({address:o.address});if(a&&!pe(a))throw new me({address:a});if(typeof n<"u"&&(typeof r<"u"||typeof s<"u"))throw new Gi;if(r&&r>Zt)throw new Me({maxFeePerGas:r});if(s&&r&&s>r)throw new ct({maxFeePerGas:r,maxPriorityFeePerGas:s})}class rc extends k{constructor(){super("`baseFeeMultiplier` must be greater than 1.",{name:"BaseFeeScalarError"})}}class vr extends k{constructor(){super("Chain does not support EIP-1559 fees.",{name:"Eip1559FeesNotSupportedError"})}}class sc extends k{constructor({maxPriorityFeePerGas:t}){super(`\`maxFeePerGas\` cannot be less than the \`maxPriorityFeePerGas\` (${te(t)} gwei).`,{name:"MaxFeePerGasTooLowError"})}}class Aa extends k{constructor({blockHash:t,blockNumber:n}){let r="Block";t&&(r=`Block at hash "${t}"`),n&&(r=`Block at number "${n}"`),super(`${r} could not be found.`,{name:"BlockNotFoundError"})}}async function le(e,{blockHash:t,blockNumber:n,blockTag:r,includeTransactions:s}={}){var f,d,l;const a=r??"latest",o=s??!1,c=n!==void 0?M(n):void 0;let i=null;if(t?i=await e.request({method:"eth_getBlockByHash",params:[t,o]},{dedupe:!0}):i=await e.request({method:"eth_getBlockByNumber",params:[c||a,o]},{dedupe:!!c}),!i)throw new Aa({blockHash:t,blockNumber:n});return(((l=(d=(f=e.chain)==null?void 0:f.formatters)==null?void 0:d.block)==null?void 0:l.format)||na)(i)}async function Er(e){const t=await e.request({method:"eth_gasPrice"});return BigInt(t)}async function ac(e,t){return Sa(e,t)}async function Sa(e,t){var a,o;const{block:n,chain:r=e.chain,request:s}=t||{};try{const c=((a=r==null?void 0:r.fees)==null?void 0:a.maxPriorityFeePerGas)??((o=r==null?void 0:r.fees)==null?void 0:o.defaultPriorityFee);if(typeof c=="function"){const u=n||await F(e,le,"getBlock")({}),f=await c({block:u,client:e,request:s});if(f===null)throw new Error;return f}if(typeof c<"u")return c;const i=await e.request({method:"eth_maxPriorityFeePerGas"});return j(i)}catch{const[c,i]=await Promise.all([n?Promise.resolve(n):F(e,le,"getBlock")({}),F(e,Er,"getGasPrice")({})]);if(typeof c.baseFeePerGas!="bigint")throw new vr;const u=i-c.baseFeePerGas;return u<0n?0n:u}}async function oc(e,t){return Un(e,t)}async function Un(e,t){var l,p;const{block:n,chain:r=e.chain,request:s,type:a="eip1559"}=t||{},o=await(async()=>{var y,h;return typeof((y=r==null?void 0:r.fees)==null?void 0:y.baseFeeMultiplier)=="function"?r.fees.baseFeeMultiplier({block:n,client:e,request:s}):((h=r==null?void 0:r.fees)==null?void 0:h.baseFeeMultiplier)??1.2})();if(o<1)throw new rc;const i=10**(((l=o.toString().split(".")[1])==null?void 0:l.length)??0),u=y=>y*BigInt(Math.ceil(o*i))/BigInt(i),f=n||await F(e,le,"getBlock")({});if(typeof((p=r==null?void 0:r.fees)==null?void 0:p.estimateFeesPerGas)=="function"){const y=await r.fees.estimateFeesPerGas({block:n,client:e,multiply:u,request:s,type:a});if(y!==null)return y}if(a==="eip1559"){if(typeof f.baseFeePerGas!="bigint")throw new vr;const y=typeof(s==null?void 0:s.maxPriorityFeePerGas)=="bigint"?s.maxPriorityFeePerGas:await Sa(e,{block:f,chain:r,request:s}),h=u(f.baseFeePerGas);return{maxFeePerGas:(s==null?void 0:s.maxFeePerGas)??h+y,maxPriorityFeePerGas:y}}return{gasPrice:(s==null?void 0:s.gasPrice)??u(await F(e,Er,"getGasPrice")({}))}}async function Xt(e,{address:t,blockTag:n="latest",blockNumber:r}){const s=await e.request({method:"eth_getTransactionCount",params:[t,r?M(r):n]},{dedupe:!!r});return V(s)}function Pr(e){const{kzg:t}=e,n=e.to??(typeof e.blobs[0]=="string"?"hex":"bytes"),r=typeof e.blobs[0]=="string"?e.blobs.map(a=>ce(a)):e.blobs,s=[];for(const a of r)s.push(Uint8Array.from(t.blobToKzgCommitment(a)));return n==="bytes"?s:s.map(a=>ne(a))}function Tr(e){const{kzg:t}=e,n=e.to??(typeof e.blobs[0]=="string"?"hex":"bytes"),r=typeof e.blobs[0]=="string"?e.blobs.map(o=>ce(o)):e.blobs,s=typeof e.commitments[0]=="string"?e.commitments.map(o=>ce(o)):e.commitments,a=[];for(let o=0;o<r.length;o++){const c=r[o],i=s[o];a.push(Uint8Array.from(t.computeBlobKzgProof(c,i)))}return n==="bytes"?a:a.map(o=>ne(o))}function Ca(e){const{commitment:t,version:n=1}=e,r=e.to??(typeof t=="string"?"hex":"bytes"),s=Ci(t,"bytes");return s.set([n],0),r==="bytes"?s:ne(s)}function Ba(e){const{commitments:t,version:n}=e,r=e.to??(typeof t[0]=="string"?"hex":"bytes"),s=[];for(const a of t)s.push(Ca({commitment:a,to:r,version:n}));return s}const As=6,ka=32,Ir=4096,$a=ka*Ir,Ss=$a*As-1-1*Ir*As,Na=1;class ic extends k{constructor({maxSize:t,size:n}){super("Blob size is too large.",{metaMessages:[`Max: ${t} bytes`,`Given: ${n} bytes`],name:"BlobSizeTooLargeError"})}}class Fa extends k{constructor(){super("Blob data must not be empty.",{name:"EmptyBlobError"})}}class cc extends k{constructor({hash:t,size:n}){super(`Versioned hash "${t}" size is invalid.`,{metaMessages:["Expected: 32",`Received: ${n}`],name:"InvalidVersionedHashSizeError"})}}class uc extends k{constructor({hash:t,version:n}){super(`Versioned hash "${t}" version is invalid.`,{metaMessages:[`Expected: ${Na}`,`Received: ${n}`],name:"InvalidVersionedHashVersionError"})}}function dc(e){const t=e.to??(typeof e.data=="string"?"hex":"bytes"),n=typeof e.data=="string"?ce(e.data):e.data,r=ft(n);if(!r)throw new Fa;if(r>Ss)throw new ic({maxSize:Ss,size:r});const s=[];let a=!0,o=0;for(;a;){const c=zt(new Uint8Array($a));let i=0;for(;i<Ir;){const u=n.slice(o,o+(ka-1));if(c.pushByte(0),c.pushBytes(u),u.length<31){c.pushByte(128),a=!1;break}i++,o+=31}s.push(c)}return t==="bytes"?s.map(c=>c.bytes):s.map(c=>ne(c.bytes))}function Ar(e){const{data:t,kzg:n,to:r}=e,s=e.blobs??dc({data:t,to:r}),a=e.commitments??Pr({blobs:s,kzg:n,to:r}),o=e.proofs??Tr({blobs:s,commitments:a,kzg:n,to:r}),c=[];for(let i=0;i<s.length;i++)c.push({blob:s[i],commitment:a[i],proof:o[i]});return c}function Ra(e){if(e.type)return e.type;if(typeof e.authorizationList<"u")return"eip7702";if(typeof e.blobs<"u"||typeof e.blobVersionedHashes<"u"||typeof e.maxFeePerBlobGas<"u"||typeof e.sidecars<"u")return"eip4844";if(typeof e.maxFeePerGas<"u"||typeof e.maxPriorityFeePerGas<"u")return"eip1559";if(typeof e.gasPrice<"u")return typeof e.accessList<"u"?"eip2930":"legacy";throw new ji({transaction:e})}async function Xe(e){const t=await e.request({method:"eth_chainId"},{dedupe:!0});return V(t)}const Ma=["blobVersionedHashes","chainId","fees","gas","nonce","type"],Cs=new Map;async function Qt(e,t){const{account:n=e.account,blobs:r,chain:s,gas:a,kzg:o,nonce:c,nonceManager:i,parameters:u=Ma,type:f}=t,d=n&&W(n),l={...t,...d?{from:d==null?void 0:d.address}:{}};let p;async function y(){return p||(p=await F(e,le,"getBlock")({blockTag:"latest"}),p)}let h;async function m(){return h||(s?s.id:typeof t.chainId<"u"?t.chainId:(h=await F(e,Xe,"getChainId")({}),h))}if(u.includes("nonce")&&typeof c>"u"&&d)if(i){const b=await m();l.nonce=await i.consume({address:d.address,chainId:b,client:e})}else l.nonce=await F(e,Xt,"getTransactionCount")({address:d.address,blockTag:"pending"});if((u.includes("blobVersionedHashes")||u.includes("sidecars"))&&r&&o){const b=Pr({blobs:r,kzg:o});if(u.includes("blobVersionedHashes")){const g=Ba({commitments:b,to:"hex"});l.blobVersionedHashes=g}if(u.includes("sidecars")){const g=Tr({blobs:r,commitments:b,kzg:o}),w=Ar({blobs:r,commitments:b,proofs:g,to:"hex"});l.sidecars=w}}if(u.includes("chainId")&&(l.chainId=await m()),(u.includes("fees")||u.includes("type"))&&typeof f>"u")try{l.type=Ra(l)}catch{let b=Cs.get(e.uid);if(typeof b>"u"){const g=await y();b=typeof(g==null?void 0:g.baseFeePerGas)=="bigint",Cs.set(e.uid,b)}l.type=b?"eip1559":"legacy"}if(u.includes("fees"))if(l.type!=="legacy"&&l.type!=="eip2930"){if(typeof l.maxFeePerGas>"u"||typeof l.maxPriorityFeePerGas>"u"){const b=await y(),{maxFeePerGas:g,maxPriorityFeePerGas:w}=await Un(e,{block:b,chain:s,request:l});if(typeof t.maxPriorityFeePerGas>"u"&&t.maxFeePerGas&&t.maxFeePerGas<w)throw new sc({maxPriorityFeePerGas:w});l.maxPriorityFeePerGas=w,l.maxFeePerGas=g}}else{if(typeof t.maxFeePerGas<"u"||typeof t.maxPriorityFeePerGas<"u")throw new vr;if(typeof t.gasPrice>"u"){const b=await y(),{gasPrice:g}=await Un(e,{block:b,chain:s,request:l,type:"legacy"});l.gasPrice=g}}return u.includes("gas")&&typeof a>"u"&&(l.gas=await F(e,Sr,"estimateGas")({...l,account:d&&{address:d.address,type:"json-rpc"}})),Le(l),delete l.parameters,l}async function za(e,{address:t,blockNumber:n,blockTag:r="latest"}){const s=n?M(n):void 0,a=await e.request({method:"eth_getBalance",params:[t,s||r]});return BigInt(a)}async function Sr(e,t){var s,a,o;const{account:n=e.account}=t,r=n?W(n):void 0;try{let A=function(B){const{block:R,request:q,rpcStateOverride:S}=B;return e.request({method:"eth_estimateGas",params:S?[q,R??"latest",S]:R?[q,R]:[q]})};const{accessList:c,authorizationList:i,blobs:u,blobVersionedHashes:f,blockNumber:d,blockTag:l,data:p,gas:y,gasPrice:h,maxFeePerBlobGas:m,maxFeePerGas:b,maxPriorityFeePerGas:g,nonce:w,value:P,stateOverride:E,...x}=await Qt(e,{...t,parameters:(r==null?void 0:r.type)==="local"?void 0:["blobVersionedHashes"]}),T=(d?M(d):void 0)||l,I=xr(E),z=await(async()=>{if(x.to)return x.to;if(i&&i.length>0)return await Ia({authorization:i[0]}).catch(()=>{throw new k("`to` is required. Could not infer from `authorizationList`")})})();Le(t);const $=(o=(a=(s=e.chain)==null?void 0:s.formatters)==null?void 0:a.transactionRequest)==null?void 0:o.format,O=($||_e)({...ht(x,{format:$}),from:r==null?void 0:r.address,accessList:c,authorizationList:i,blobs:u,blobVersionedHashes:f,data:p,gas:y,gasPrice:h,maxFeePerBlobGas:m,maxFeePerGas:b,maxPriorityFeePerGas:g,nonce:w,to:z,value:P});let C=BigInt(await A({block:T,request:O,rpcStateOverride:I}));if(i){const B=await za(e,{address:O.from}),R=await Promise.all(i.map(async q=>{const{address:S}=q,L=await A({block:T,request:{authorizationList:void 0,data:p,from:r==null?void 0:r.address,to:S,value:M(B)},rpcStateOverride:I}).catch(()=>100000n);return 2n*BigInt(L)}));C+=R.reduce((q,S)=>q+S,0n)}return C}catch(c){throw tc(c,{...t,account:r,chain:e.chain})}}async function qa(e,t){const{abi:n,address:r,args:s,functionName:a,dataSuffix:o,...c}=t,i=oe({abi:n,args:s,functionName:a});try{return await F(e,Sr,"estimateGas")({data:`${i}${o?o.replace("0x",""):""}`,to:r,...c})}catch(u){const f=c.account?W(c.account):void 0;throw Re(u,{abi:n,address:r,args:s,docsPath:"/docs/contract/estimateContractGas",functionName:a,sender:f==null?void 0:f.address})}}async function Cr(e,{address:t,blockHash:n,fromBlock:r,toBlock:s,event:a,events:o,args:c,strict:i}={}){const u=i??!1,f=o??(a?[a]:void 0);let d=[];f&&(d=[f.flatMap(h=>ut({abi:[h],eventName:h.name,args:o?void 0:c}))],a&&(d=d[0]));let l;n?l=await e.request({method:"eth_getLogs",params:[{address:t,topics:d,blockHash:n}]}):l=await e.request({method:"eth_getLogs",params:[{address:t,topics:d,fromBlock:typeof r=="bigint"?M(r):r,toBlock:typeof s=="bigint"?M(s):s}]});const p=l.map(y=>Ie(y));return f?pr({abi:f,args:c,logs:p,strict:u}):p}async function Br(e,t){const{abi:n,address:r,args:s,blockHash:a,eventName:o,fromBlock:c,toBlock:i,strict:u}=t,f=o?Ke({abi:n,name:o}):void 0,d=f?void 0:n.filter(l=>l.type==="event");return F(e,Cr,"getLogs")({address:r,args:s,blockHash:a,event:f,events:d,fromBlock:c,toBlock:i,strict:u})}const mn="/docs/contract/decodeFunctionResult";function Ge(e){const{abi:t,args:n,functionName:r,data:s}=e;let a=t[0];if(r){const c=Ke({abi:t,args:n,name:r});if(!c)throw new We(r,{docsPath:mn});a=c}if(a.type!=="function")throw new We(void 0,{docsPath:mn});if(!a.outputs)throw new ra(a.name,{docsPath:mn});const o=Ht(a.outputs,s);if(o&&o.length>1)return o;if(o&&o.length===1)return o[0]}const Vn=[{inputs:[{components:[{name:"target",type:"address"},{name:"allowFailure",type:"bool"},{name:"callData",type:"bytes"}],name:"calls",type:"tuple[]"}],name:"aggregate3",outputs:[{components:[{name:"success",type:"bool"},{name:"returnData",type:"bytes"}],name:"returnData",type:"tuple[]"}],stateMutability:"view",type:"function"}],Wn=[{name:"query",type:"function",stateMutability:"view",inputs:[{type:"tuple[]",name:"queries",components:[{type:"address",name:"sender"},{type:"string[]",name:"urls"},{type:"bytes",name:"data"}]}],outputs:[{type:"bool[]",name:"failures"},{type:"bytes[]",name:"responses"}]},{name:"HttpError",type:"error",inputs:[{type:"uint16",name:"status"},{type:"string",name:"message"}]}],_a=[{inputs:[],name:"ResolverNotFound",type:"error"},{inputs:[],name:"ResolverWildcardNotSupported",type:"error"},{inputs:[],name:"ResolverNotContract",type:"error"},{inputs:[{name:"returnData",type:"bytes"}],name:"ResolverError",type:"error"},{inputs:[{components:[{name:"status",type:"uint16"},{name:"message",type:"string"}],name:"errors",type:"tuple[]"}],name:"HttpError",type:"error"}],Oa=[..._a,{name:"resolve",type:"function",stateMutability:"view",inputs:[{name:"name",type:"bytes"},{name:"data",type:"bytes"}],outputs:[{name:"",type:"bytes"},{name:"address",type:"address"}]},{name:"resolve",type:"function",stateMutability:"view",inputs:[{name:"name",type:"bytes"},{name:"data",type:"bytes"},{name:"gateways",type:"string[]"}],outputs:[{name:"",type:"bytes"},{name:"address",type:"address"}]}],fc=[..._a,{name:"reverse",type:"function",stateMutability:"view",inputs:[{type:"bytes",name:"reverseName"}],outputs:[{type:"string",name:"resolvedName"},{type:"address",name:"resolvedAddress"},{type:"address",name:"reverseResolver"},{type:"address",name:"resolver"}]},{name:"reverse",type:"function",stateMutability:"view",inputs:[{type:"bytes",name:"reverseName"},{type:"string[]",name:"gateways"}],outputs:[{type:"string",name:"resolvedName"},{type:"address",name:"resolvedAddress"},{type:"address",name:"reverseResolver"},{type:"address",name:"resolver"}]}],Bs=[{name:"text",type:"function",stateMutability:"view",inputs:[{name:"name",type:"bytes32"},{name:"key",type:"string"}],outputs:[{name:"",type:"string"}]}],ks=[{name:"addr",type:"function",stateMutability:"view",inputs:[{name:"name",type:"bytes32"}],outputs:[{name:"",type:"address"}]},{name:"addr",type:"function",stateMutability:"view",inputs:[{name:"name",type:"bytes32"},{name:"coinType",type:"uint256"}],outputs:[{name:"",type:"bytes"}]}],$s=[{inputs:[{name:"_signer",type:"address"},{name:"_hash",type:"bytes32"},{name:"_signature",type:"bytes"}],stateMutability:"nonpayable",type:"constructor"},{inputs:[{name:"_signer",type:"address"},{name:"_hash",type:"bytes32"},{name:"_signature",type:"bytes"}],outputs:[{type:"bool"}],stateMutability:"nonpayable",type:"function",name:"isValidSig"}],ql=[{type:"event",name:"Approval",inputs:[{indexed:!0,name:"owner",type:"address"},{indexed:!0,name:"spender",type:"address"},{indexed:!1,name:"value",type:"uint256"}]},{type:"event",name:"Transfer",inputs:[{indexed:!0,name:"from",type:"address"},{indexed:!0,name:"to",type:"address"},{indexed:!1,name:"value",type:"uint256"}]},{type:"function",name:"allowance",stateMutability:"view",inputs:[{name:"owner",type:"address"},{name:"spender",type:"address"}],outputs:[{type:"uint256"}]},{type:"function",name:"approve",stateMutability:"nonpayable",inputs:[{name:"spender",type:"address"},{name:"amount",type:"uint256"}],outputs:[{type:"bool"}]},{type:"function",name:"balanceOf",stateMutability:"view",inputs:[{name:"account",type:"address"}],outputs:[{type:"uint256"}]},{type:"function",name:"decimals",stateMutability:"view",inputs:[],outputs:[{type:"uint8"}]},{type:"function",name:"name",stateMutability:"view",inputs:[],outputs:[{type:"string"}]},{type:"function",name:"symbol",stateMutability:"view",inputs:[],outputs:[{type:"string"}]},{type:"function",name:"totalSupply",stateMutability:"view",inputs:[],outputs:[{type:"uint256"}]},{type:"function",name:"transfer",stateMutability:"nonpayable",inputs:[{name:"recipient",type:"address"},{name:"amount",type:"uint256"}],outputs:[{type:"bool"}]},{type:"function",name:"transferFrom",stateMutability:"nonpayable",inputs:[{name:"sender",type:"address"},{name:"recipient",type:"address"},{name:"amount",type:"uint256"}],outputs:[{type:"bool"}]}],_l=[{type:"event",name:"Approval",inputs:[{indexed:!0,name:"owner",type:"address"},{indexed:!0,name:"spender",type:"address"},{indexed:!1,name:"value",type:"uint256"}]},{type:"event",name:"Transfer",inputs:[{indexed:!0,name:"from",type:"address"},{indexed:!0,name:"to",type:"address"},{indexed:!1,name:"value",type:"uint256"}]},{type:"function",name:"allowance",stateMutability:"view",inputs:[{name:"owner",type:"address"},{name:"spender",type:"address"}],outputs:[{type:"uint256"}]},{type:"function",name:"approve",stateMutability:"nonpayable",inputs:[{name:"spender",type:"address"},{name:"amount",type:"uint256"}],outputs:[{type:"bool"}]},{type:"function",name:"balanceOf",stateMutability:"view",inputs:[{name:"account",type:"address"}],outputs:[{type:"uint256"}]},{type:"function",name:"decimals",stateMutability:"view",inputs:[],outputs:[{type:"uint8"}]},{type:"function",name:"name",stateMutability:"view",inputs:[],outputs:[{type:"bytes32"}]},{type:"function",name:"symbol",stateMutability:"view",inputs:[],outputs:[{type:"bytes32"}]},{type:"function",name:"totalSupply",stateMutability:"view",inputs:[],outputs:[{type:"uint256"}]},{type:"function",name:"transfer",stateMutability:"nonpayable",inputs:[{name:"recipient",type:"address"},{name:"amount",type:"uint256"}],outputs:[{type:"bool"}]},{type:"function",name:"transferFrom",stateMutability:"nonpayable",inputs:[{name:"sender",type:"address"},{name:"recipient",type:"address"},{name:"amount",type:"uint256"}],outputs:[{type:"bool"}]}],Ol=[{inputs:[{internalType:"address",name:"sender",type:"address"},{internalType:"uint256",name:"balance",type:"uint256"},{internalType:"uint256",name:"needed",type:"uint256"},{internalType:"uint256",name:"tokenId",type:"uint256"}],name:"ERC1155InsufficientBalance",type:"error"},{inputs:[{internalType:"address",name:"approver",type:"address"}],name:"ERC1155InvalidApprover",type:"error"},{inputs:[{internalType:"uint256",name:"idsLength",type:"uint256"},{internalType:"uint256",name:"valuesLength",type:"uint256"}],name:"ERC1155InvalidArrayLength",type:"error"},{inputs:[{internalType:"address",name:"operator",type:"address"}],name:"ERC1155InvalidOperator",type:"error"},{inputs:[{internalType:"address",name:"receiver",type:"address"}],name:"ERC1155InvalidReceiver",type:"error"},{inputs:[{internalType:"address",name:"sender",type:"address"}],name:"ERC1155InvalidSender",type:"error"},{inputs:[{internalType:"address",name:"operator",type:"address"},{internalType:"address",name:"owner",type:"address"}],name:"ERC1155MissingApprovalForAll",type:"error"},{anonymous:!1,inputs:[{indexed:!0,internalType:"address",name:"account",type:"address"},{indexed:!0,internalType:"address",name:"operator",type:"address"},{indexed:!1,internalType:"bool",name:"approved",type:"bool"}],name:"ApprovalForAll",type:"event"},{anonymous:!1,inputs:[{indexed:!0,internalType:"address",name:"operator",type:"address"},{indexed:!0,internalType:"address",name:"from",type:"address"},{indexed:!0,internalType:"address",name:"to",type:"address"},{indexed:!1,internalType:"uint256[]",name:"ids",type:"uint256[]"},{indexed:!1,internalType:"uint256[]",name:"values",type:"uint256[]"}],name:"TransferBatch",type:"event"},{anonymous:!1,inputs:[{indexed:!0,internalType:"address",name:"operator",type:"address"},{indexed:!0,internalType:"address",name:"from",type:"address"},{indexed:!0,internalType:"address",name:"to",type:"address"},{indexed:!1,internalType:"uint256",name:"id",type:"uint256"},{indexed:!1,internalType:"uint256",name:"value",type:"uint256"}],name:"TransferSingle",type:"event"},{anonymous:!1,inputs:[{indexed:!1,internalType:"string",name:"value",type:"string"},{indexed:!0,internalType:"uint256",name:"id",type:"uint256"}],name:"URI",type:"event"},{inputs:[{internalType:"address",name:"account",type:"address"},{internalType:"uint256",name:"id",type:"uint256"}],name:"balanceOf",outputs:[{internalType:"uint256",name:"",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{internalType:"address[]",name:"accounts",type:"address[]"},{internalType:"uint256[]",name:"ids",type:"uint256[]"}],name:"balanceOfBatch",outputs:[{internalType:"uint256[]",name:"",type:"uint256[]"}],stateMutability:"view",type:"function"},{inputs:[{internalType:"address",name:"account",type:"address"},{internalType:"address",name:"operator",type:"address"}],name:"isApprovedForAll",outputs:[{internalType:"bool",name:"",type:"bool"}],stateMutability:"view",type:"function"},{inputs:[{internalType:"address",name:"from",type:"address"},{internalType:"address",name:"to",type:"address"},{internalType:"uint256[]",name:"ids",type:"uint256[]"},{internalType:"uint256[]",name:"values",type:"uint256[]"},{internalType:"bytes",name:"data",type:"bytes"}],name:"safeBatchTransferFrom",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{internalType:"address",name:"from",type:"address"},{internalType:"address",name:"to",type:"address"},{internalType:"uint256",name:"id",type:"uint256"},{internalType:"uint256",name:"value",type:"uint256"},{internalType:"bytes",name:"data",type:"bytes"}],name:"safeTransferFrom",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{internalType:"address",name:"operator",type:"address"},{internalType:"bool",name:"approved",type:"bool"}],name:"setApprovalForAll",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{internalType:"bytes4",name:"interfaceId",type:"bytes4"}],name:"supportsInterface",outputs:[{internalType:"bool",name:"",type:"bool"}],stateMutability:"view",type:"function"},{inputs:[{internalType:"uint256",name:"",type:"uint256"}],name:"uri",outputs:[{internalType:"string",name:"",type:"string"}],stateMutability:"view",type:"function"}],Ll=[{type:"event",name:"Approval",inputs:[{indexed:!0,name:"owner",type:"address"},{indexed:!0,name:"spender",type:"address"},{indexed:!0,name:"tokenId",type:"uint256"}]},{type:"event",name:"ApprovalForAll",inputs:[{indexed:!0,name:"owner",type:"address"},{indexed:!0,name:"operator",type:"address"},{indexed:!1,name:"approved",type:"bool"}]},{type:"event",name:"Transfer",inputs:[{indexed:!0,name:"from",type:"address"},{indexed:!0,name:"to",type:"address"},{indexed:!0,name:"tokenId",type:"uint256"}]},{type:"function",name:"approve",stateMutability:"payable",inputs:[{name:"spender",type:"address"},{name:"tokenId",type:"uint256"}],outputs:[]},{type:"function",name:"balanceOf",stateMutability:"view",inputs:[{name:"account",type:"address"}],outputs:[{type:"uint256"}]},{type:"function",name:"getApproved",stateMutability:"view",inputs:[{name:"tokenId",type:"uint256"}],outputs:[{type:"address"}]},{type:"function",name:"isApprovedForAll",stateMutability:"view",inputs:[{name:"owner",type:"address"},{name:"operator",type:"address"}],outputs:[{type:"bool"}]},{type:"function",name:"name",stateMutability:"view",inputs:[],outputs:[{type:"string"}]},{type:"function",name:"ownerOf",stateMutability:"view",inputs:[{name:"tokenId",type:"uint256"}],outputs:[{name:"owner",type:"address"}]},{type:"function",name:"safeTransferFrom",stateMutability:"payable",inputs:[{name:"from",type:"address"},{name:"to",type:"address"},{name:"tokenId",type:"uint256"}],outputs:[]},{type:"function",name:"safeTransferFrom",stateMutability:"nonpayable",inputs:[{name:"from",type:"address"},{name:"to",type:"address"},{name:"id",type:"uint256"},{name:"data",type:"bytes"}],outputs:[]},{type:"function",name:"setApprovalForAll",stateMutability:"nonpayable",inputs:[{name:"operator",type:"address"},{name:"approved",type:"bool"}],outputs:[]},{type:"function",name:"symbol",stateMutability:"view",inputs:[],outputs:[{type:"string"}]},{type:"function",name:"tokenByIndex",stateMutability:"view",inputs:[{name:"index",type:"uint256"}],outputs:[{type:"uint256"}]},{type:"function",name:"tokenByIndex",stateMutability:"view",inputs:[{name:"owner",type:"address"},{name:"index",type:"uint256"}],outputs:[{name:"tokenId",type:"uint256"}]},{type:"function",name:"tokenURI",stateMutability:"view",inputs:[{name:"tokenId",type:"uint256"}],outputs:[{type:"string"}]},{type:"function",name:"totalSupply",stateMutability:"view",inputs:[],outputs:[{type:"uint256"}]},{type:"function",name:"transferFrom",stateMutability:"payable",inputs:[{name:"sender",type:"address"},{name:"recipient",type:"address"},{name:"tokeId",type:"uint256"}],outputs:[]}],Gl=[{anonymous:!1,inputs:[{indexed:!0,name:"owner",type:"address"},{indexed:!0,name:"spender",type:"address"},{indexed:!1,name:"value",type:"uint256"}],name:"Approval",type:"event"},{anonymous:!1,inputs:[{indexed:!0,name:"sender",type:"address"},{indexed:!0,name:"receiver",type:"address"},{indexed:!1,name:"assets",type:"uint256"},{indexed:!1,name:"shares",type:"uint256"}],name:"Deposit",type:"event"},{anonymous:!1,inputs:[{indexed:!0,name:"from",type:"address"},{indexed:!0,name:"to",type:"address"},{indexed:!1,name:"value",type:"uint256"}],name:"Transfer",type:"event"},{anonymous:!1,inputs:[{indexed:!0,name:"sender",type:"address"},{indexed:!0,name:"receiver",type:"address"},{indexed:!0,name:"owner",type:"address"},{indexed:!1,name:"assets",type:"uint256"},{indexed:!1,name:"shares",type:"uint256"}],name:"Withdraw",type:"event"},{inputs:[{name:"owner",type:"address"},{name:"spender",type:"address"}],name:"allowance",outputs:[{type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"spender",type:"address"},{name:"amount",type:"uint256"}],name:"approve",outputs:[{type:"bool"}],stateMutability:"nonpayable",type:"function"},{inputs:[],name:"asset",outputs:[{name:"assetTokenAddress",type:"address"}],stateMutability:"view",type:"function"},{inputs:[{name:"account",type:"address"}],name:"balanceOf",outputs:[{type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"shares",type:"uint256"}],name:"convertToAssets",outputs:[{name:"assets",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"assets",type:"uint256"}],name:"convertToShares",outputs:[{name:"shares",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"assets",type:"uint256"},{name:"receiver",type:"address"}],name:"deposit",outputs:[{name:"shares",type:"uint256"}],stateMutability:"nonpayable",type:"function"},{inputs:[{name:"caller",type:"address"}],name:"maxDeposit",outputs:[{name:"maxAssets",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"caller",type:"address"}],name:"maxMint",outputs:[{name:"maxShares",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"owner",type:"address"}],name:"maxRedeem",outputs:[{name:"maxShares",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"owner",type:"address"}],name:"maxWithdraw",outputs:[{name:"maxAssets",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"shares",type:"uint256"},{name:"receiver",type:"address"}],name:"mint",outputs:[{name:"assets",type:"uint256"}],stateMutability:"nonpayable",type:"function"},{inputs:[{name:"assets",type:"uint256"}],name:"previewDeposit",outputs:[{name:"shares",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"shares",type:"uint256"}],name:"previewMint",outputs:[{name:"assets",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"shares",type:"uint256"}],name:"previewRedeem",outputs:[{name:"assets",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"assets",type:"uint256"}],name:"previewWithdraw",outputs:[{name:"shares",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"shares",type:"uint256"},{name:"receiver",type:"address"},{name:"owner",type:"address"}],name:"redeem",outputs:[{name:"assets",type:"uint256"}],stateMutability:"nonpayable",type:"function"},{inputs:[],name:"totalAssets",outputs:[{name:"totalManagedAssets",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[],name:"totalSupply",outputs:[{type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{name:"to",type:"address"},{name:"amount",type:"uint256"}],name:"transfer",outputs:[{type:"bool"}],stateMutability:"nonpayable",type:"function"},{inputs:[{name:"from",type:"address"},{name:"to",type:"address"},{name:"amount",type:"uint256"}],name:"transferFrom",outputs:[{type:"bool"}],stateMutability:"nonpayable",type:"function"},{inputs:[{name:"assets",type:"uint256"},{name:"receiver",type:"address"},{name:"owner",type:"address"}],name:"withdraw",outputs:[{name:"shares",type:"uint256"}],stateMutability:"nonpayable",type:"function"}],lc="0x82ad56cb",La="0x608060405234801561001057600080fd5b5060405161018e38038061018e83398101604081905261002f91610124565b6000808351602085016000f59050803b61004857600080fd5b6000808351602085016000855af16040513d6000823e81610067573d81fd5b3d81f35b634e487b7160e01b600052604160045260246000fd5b600082601f83011261009257600080fd5b81516001600160401b038111156100ab576100ab61006b565b604051601f8201601f19908116603f011681016001600160401b03811182821017156100d9576100d961006b565b6040528181528382016020018510156100f157600080fd5b60005b82811015610110576020818601810151838301820152016100f4565b506000918101602001919091529392505050565b6000806040838503121561013757600080fd5b82516001600160401b0381111561014d57600080fd5b61015985828601610081565b602085015190935090506001600160401b0381111561017757600080fd5b61018385828601610081565b915050925092905056fe",pc="0x608060405234801561001057600080fd5b506040516102c03803806102c083398101604081905261002f916101e6565b836001600160a01b03163b6000036100e457600080836001600160a01b03168360405161005c9190610270565b6000604051808303816000865af19150503d8060008114610099576040519150601f19603f3d011682016040523d82523d6000602084013e61009e565b606091505b50915091508115806100b857506001600160a01b0386163b155b156100e1578060405163101bb98d60e01b81526004016100d8919061028c565b60405180910390fd5b50505b6000808451602086016000885af16040513d6000823e81610103573d81fd5b3d81f35b80516001600160a01b038116811461011e57600080fd5b919050565b634e487b7160e01b600052604160045260246000fd5b60005b8381101561015457818101518382015260200161013c565b50506000910152565b600082601f83011261016e57600080fd5b81516001600160401b0381111561018757610187610123565b604051601f8201601f19908116603f011681016001600160401b03811182821017156101b5576101b5610123565b6040528181528382016020018510156101cd57600080fd5b6101de826020830160208701610139565b949350505050565b600080600080608085870312156101fc57600080fd5b61020585610107565b60208601519094506001600160401b0381111561022157600080fd5b61022d8782880161015d565b93505061023c60408601610107565b60608601519092506001600160401b0381111561025857600080fd5b6102648782880161015d565b91505092959194509250565b60008251610282818460208701610139565b9190910192915050565b60208152600082518060208401526102ab816040850160208701610139565b601f01601f1916919091016040019291505056fe",mc="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";class Zn extends k{constructor({blockNumber:t,chain:n,contract:r}){super(`Chain "${n.name}" does not support contract "${r.name}".`,{metaMessages:["This could be due to any of the following:",...t&&r.blockCreated&&r.blockCreated>t?[`- The contract "${r.name}" was not deployed until block ${r.blockCreated} (current block ${t}).`]:[`- The chain does not have the contract "${r.name}" configured.`]],name:"ChainDoesNotSupportContract"})}}class hc extends k{constructor({chain:t,currentChainId:n}){super(`The current chain of the wallet (id: ${n}) does not match the target chain for the transaction (id: ${t.id} – ${t.name}).`,{metaMessages:[`Current Chain ID:  ${n}`,`Expected Chain ID: ${t.id} – ${t.name}`],name:"ChainMismatchError"})}}class yc extends k{constructor(){super(["No chain was provided to the request.","Please provide a chain with the `chain` argument on the Action, or by supplying a `chain` to WalletClient."].join(`
`),{name:"ChainNotFoundError"})}}class Ga extends k{constructor(){super("No chain was provided to the Client.",{name:"ClientChainNotConfiguredError"})}}class en extends k{constructor({chainId:t}){super(typeof t=="number"?`Chain ID "${t}" is invalid.`:"Chain ID is invalid.",{name:"InvalidChainIdError"})}}const hn="/docs/contract/encodeDeployData";function tn(e){const{abi:t,args:n,bytecode:r}=e;if(!n||n.length===0)return r;const s=t.find(o=>"type"in o&&o.type==="constructor");if(!s)throw new sa({docsPath:hn});if(!("inputs"in s))throw new qt({docsPath:hn});if(!s.inputs||s.inputs.length===0)throw new qt({docsPath:hn});const a=Ye(s.inputs,n);return ue([r,a])}function Qe({blockNumber:e,chain:t,contract:n}){var s;const r=(s=t==null?void 0:t.contracts)==null?void 0:s[n];if(!r)throw new Zn({chain:t,contract:{name:n}});if(e&&r.blockCreated&&r.blockCreated>e)throw new Zn({blockNumber:e,chain:t,contract:{name:n,blockCreated:r.blockCreated}});return r.address}function ja(e,{docsPath:t,...n}){const r=(()=>{const s=Jt(e,n);return s instanceof mt?e:s})();return new Ta(r,{docsPath:t,...n})}function kr(){let e=()=>{},t=()=>{};return{promise:new Promise((r,s)=>{e=r,t=s}),resolve:e,reject:t}}const yn=new Map;function $r({fn:e,id:t,shouldSplitBatch:n,wait:r=0,sort:s}){const a=async()=>{const f=i();o();const d=f.map(({args:l})=>l);d.length!==0&&e(d).then(l=>{s&&Array.isArray(l)&&l.sort(s);for(let p=0;p<f.length;p++){const{resolve:y}=f[p];y==null||y([l[p],l])}}).catch(l=>{for(let p=0;p<f.length;p++){const{reject:y}=f[p];y==null||y(l)}})},o=()=>yn.delete(t),c=()=>i().map(({args:f})=>f),i=()=>yn.get(t)||[],u=f=>yn.set(t,[...i(),f]);return{flush:o,async schedule(f){const{promise:d,resolve:l,reject:p}=kr();return(n==null?void 0:n([...c(),f]))&&a(),i().length>0?(u({args:f,resolve:l,reject:p}),d):(u({args:f,resolve:l,reject:p}),setTimeout(a,r),d)}}}async function yt(e,t){var $,N,O,A;const{account:n=e.account,batch:r=!!(($=e.batch)!=null&&$.multicall),blockNumber:s,blockTag:a="latest",accessList:o,blobs:c,code:i,data:u,factory:f,factoryData:d,gas:l,gasPrice:p,maxFeePerBlobGas:y,maxFeePerGas:h,maxPriorityFeePerGas:m,nonce:b,to:g,value:w,stateOverride:P,...E}=t,x=n?W(n):void 0;if(i&&(f||d))throw new k("Cannot provide both `code` & `factory`/`factoryData` as parameters.");if(i&&g)throw new k("Cannot provide both `code` & `to` as parameters.");const v=i&&u,T=f&&d&&g&&u,I=v||T,z=v?wc({code:i,data:u}):T?xc({data:u,factory:f,factoryData:d,to:g}):u;try{Le(t);const B=(s?M(s):void 0)||a,R=xr(P),q=(A=(O=(N=e.chain)==null?void 0:N.formatters)==null?void 0:O.transactionRequest)==null?void 0:A.format,L=(q||_e)({...ht(E,{format:q}),from:x==null?void 0:x.address,accessList:o,blobs:c,data:z,gas:l,gasPrice:p,maxFeePerBlobGas:y,maxFeePerGas:h,maxPriorityFeePerGas:m,nonce:b,to:I?void 0:g,value:w});if(r&&bc({request:L})&&!R)try{return await gc(e,{...L,blockNumber:s,blockTag:a})}catch(Z){if(!(Z instanceof Ga)&&!(Z instanceof Zn))throw Z}const D=await e.request({method:"eth_call",params:R?[L,B,R]:[L,B]});return D==="0x"?{data:void 0}:{data:D}}catch(C){const B=vc(C),{offchainLookup:R,offchainLookupSignature:q}=await lr(async()=>{const{offchainLookup:S,offchainLookupSignature:L}=await Promise.resolve().then(()=>gu);return{offchainLookup:S,offchainLookupSignature:L}},void 0);if(e.ccipRead!==!1&&(B==null?void 0:B.slice(0,10))===q&&g)return{data:await R(e,{data:B,to:g})};throw I&&(B==null?void 0:B.slice(0,10))==="0x101bb98d"?new Ki({factory:f}):ja(C,{...t,account:x,chain:e.chain})}}function bc({request:e}){const{data:t,to:n,...r}=e;return!(!t||t.startsWith(lc)||!n||Object.values(r).filter(s=>typeof s<"u").length>0)}async function gc(e,t){var h;const{batchSize:n=1024,wait:r=0}=typeof((h=e.batch)==null?void 0:h.multicall)=="object"?e.batch.multicall:{},{blockNumber:s,blockTag:a="latest",data:o,multicallAddress:c,to:i}=t;let u=c;if(!u){if(!e.chain)throw new Ga;u=Qe({blockNumber:s,chain:e.chain,contract:"multicall3"})}const d=(s?M(s):void 0)||a,{schedule:l}=$r({id:`${e.uid}.${d}`,wait:r,shouldSplitBatch(m){return m.reduce((g,{data:w})=>g+(w.length-2),0)>n*2},fn:async m=>{const b=m.map(P=>({allowFailure:!0,callData:P.data,target:P.to})),g=oe({abi:Vn,args:[b],functionName:"aggregate3"}),w=await e.request({method:"eth_call",params:[{data:g,to:u},d]});return Ge({abi:Vn,args:[b],functionName:"aggregate3",data:w||"0x"})}}),[{returnData:p,success:y}]=await l({data:o,to:i});if(!y)throw new Yt({data:p});return p==="0x"?{data:void 0}:{data:p}}function wc(e){const{code:t,data:n}=e;return tn({abi:aa(["constructor(bytes, bytes)"]),bytecode:La,args:[t,n]})}function xc(e){const{data:t,factory:n,factoryData:r,to:s}=e;return tn({abi:aa(["constructor(address, bytes, address, bytes)"]),bytecode:pc,args:[s,t,n,r]})}function vc(e){var n;if(!(e instanceof k))return;const t=e.walk();return typeof(t==null?void 0:t.data)=="object"?(n=t.data)==null?void 0:n.data:t.data}async function he(e,t){const{abi:n,address:r,args:s,functionName:a,...o}=t,c=oe({abi:n,args:s,functionName:a});try{const{data:i}=await F(e,yt,"call")({...o,data:c,to:r});return Ge({abi:n,args:s,functionName:a,data:i||"0x"})}catch(i){throw Re(i,{abi:n,address:r,args:s,docsPath:"/docs/contract/readContract",functionName:a})}}async function Da(e,t){const{abi:n,address:r,args:s,dataSuffix:a,functionName:o,...c}=t,i=c.account?W(c.account):e.account,u=oe({abi:n,args:s,functionName:o});try{const{data:f}=await F(e,yt,"call")({batch:!1,data:`${u}${a?a.replace("0x",""):""}`,to:r,...c,account:i}),d=Ge({abi:n,args:s,functionName:o,data:f||"0x"}),l=n.filter(p=>"name"in p&&p.name===t.functionName);return{result:d,request:{abi:l,address:r,args:s,dataSuffix:a,functionName:o,...c,account:i}}}catch(f){throw Re(f,{abi:n,address:r,args:s,docsPath:"/docs/contract/simulateContract",functionName:o,sender:i==null?void 0:i.address})}}const bn=new Map,Ns=new Map;let Ec=0;function Pe(e,t,n){const r=++Ec,s=()=>bn.get(e)||[],a=()=>{const f=s();bn.set(e,f.filter(d=>d.id!==r))},o=()=>{const f=s();if(!f.some(l=>l.id===r))return;const d=Ns.get(e);f.length===1&&d&&d(),a()},c=s();if(bn.set(e,[...c,{id:r,fns:t}]),c&&c.length>0)return o;const i={};for(const f in t)i[f]=(...d)=>{var p,y;const l=s();if(l.length!==0)for(const h of l)(y=(p=h.fns)[f])==null||y.call(p,...d)};const u=n(i);return typeof u=="function"&&Ns.set(e,u),o}async function Dt(e){return new Promise(t=>setTimeout(t,e))}function et(e,{emitOnBegin:t,initialWaitTime:n,interval:r}){let s=!0;const a=()=>s=!1;return(async()=>{let c;t&&(c=await e({unpoll:a}));const i=await(n==null?void 0:n(c))??r;await Dt(i);const u=async()=>{s&&(await e({unpoll:a}),await Dt(r),u())};u()})(),a}const Pc=new Map,Tc=new Map;function Ic(e){const t=(s,a)=>({clear:()=>a.delete(s),get:()=>a.get(s),set:o=>a.set(s,o)}),n=t(e,Pc),r=t(e,Tc);return{clear:()=>{n.clear(),r.clear()},promise:n,response:r}}async function Ac(e,{cacheKey:t,cacheTime:n=Number.POSITIVE_INFINITY}){const r=Ic(t),s=r.response.get();if(s&&n>0&&new Date().getTime()-s.created.getTime()<n)return s.data;let a=r.promise.get();a||(a=e(),r.promise.set(a));try{const o=await a;return r.response.set({created:new Date,data:o}),o}finally{r.promise.clear()}}const Sc=e=>`blockNumber.${e}`;async function bt(e,{cacheTime:t=e.cacheTime}={}){const n=await Ac(()=>e.request({method:"eth_blockNumber"}),{cacheKey:Sc(e.uid),cacheTime:t});return BigInt(n)}async function nn(e,{filter:t}){const n="strict"in t&&t.strict,r=await t.request({method:"eth_getFilterChanges",params:[t.id]});if(typeof r[0]=="string")return r;const s=r.map(a=>Ie(a));return!("abi"in t)||!t.abi?s:pr({abi:t.abi,logs:s,strict:n})}async function rn(e,{filter:t}){return t.request({method:"eth_uninstallFilter",params:[t.id]})}function Ha(e,t){const{abi:n,address:r,args:s,batch:a=!0,eventName:o,fromBlock:c,onError:i,onLogs:u,poll:f,pollingInterval:d=e.pollingInterval,strict:l}=t;return(typeof f<"u"?f:typeof c=="bigint"?!0:!(e.transport.type==="webSocket"||e.transport.type==="fallback"&&e.transport.transports[0].config.type==="webSocket"))?(()=>{const m=l??!1,b=X(["watchContractEvent",r,s,a,e.uid,o,d,m,c]);return Pe(b,{onLogs:u,onError:i},g=>{let w;c!==void 0&&(w=c-1n);let P,E=!1;const x=et(async()=>{var v;if(!E){try{P=await F(e,gr,"createContractEventFilter")({abi:n,address:r,args:s,eventName:o,strict:m,fromBlock:c})}catch{}E=!0;return}try{let T;if(P)T=await F(e,nn,"getFilterChanges")({filter:P});else{const I=await F(e,bt,"getBlockNumber")({});w&&w<I?T=await F(e,Br,"getContractEvents")({abi:n,address:r,args:s,eventName:o,fromBlock:w+1n,toBlock:I,strict:m}):T=[],w=I}if(T.length===0)return;if(a)g.onLogs(T);else for(const I of T)g.onLogs([I])}catch(T){P&&T instanceof Gt&&(E=!1),(v=g.onError)==null||v.call(g,T)}},{emitOnBegin:!0,interval:d});return async()=>{P&&await F(e,rn,"uninstallFilter")({filter:P}),x()}})})():(()=>{const m=l??!1,b=X(["watchContractEvent",r,s,a,e.uid,o,d,m]);let g=!0,w=()=>g=!1;return Pe(b,{onLogs:u,onError:i},P=>((async()=>{try{const E=(()=>{if(e.transport.type==="fallback"){const T=e.transport.transports.find(I=>I.config.type==="webSocket");return T?T.value:e.transport}return e.transport})(),x=o?ut({abi:n,eventName:o,args:s}):[],{unsubscribe:v}=await E.subscribe({params:["logs",{address:r,topics:x}],onData(T){var z;if(!g)return;const I=T.result;try{const{eventName:$,args:N}=oa({abi:n,data:I.data,topics:I.topics,strict:l}),O=Ie(I,{args:N,eventName:$});P.onLogs([O])}catch($){let N,O;if($ instanceof ia||$ instanceof ca){if(l)return;N=$.abiItem.name,O=(z=$.abiItem.inputs)==null?void 0:z.some(C=>!("name"in C&&C.name))}const A=Ie(I,{args:O?[]:{},eventName:N});P.onLogs([A])}},onError(T){var I;(I=P.onError)==null||I.call(P,T)}});w=v,g||w()}catch(E){i==null||i(E)}})(),()=>w()))})()}class Se extends k{constructor({docsPath:t}={}){super(["Could not find an Account to execute with this Action.","Please provide an Account with the `account` argument on the Action, or by supplying an `account` to the Client."].join(`
`),{docsPath:t,docsSlug:"account",name:"AccountNotFoundError"})}}class $t extends k{constructor({docsPath:t,metaMessages:n,type:r}){super(`Account type "${r}" is not supported.`,{docsPath:t,metaMessages:n,name:"AccountTypeNotSupportedError"})}}function Ua({chain:e,currentChainId:t}){if(!e)throw new yc;if(t!==e.id)throw new hc({chain:e,currentChainId:t})}function Va(e,{docsPath:t,...n}){const r=(()=>{const s=Jt(e,n);return s instanceof mt?e:s})();return new Ui(r,{docsPath:t,...n})}async function Nr(e,{serializedTransaction:t}){return e.request({method:"eth_sendRawTransaction",params:[t]},{retryCount:0})}const gn=new mr(128);async function Fr(e,t){var b,g,w,P;const{account:n=e.account,chain:r=e.chain,accessList:s,authorizationList:a,blobs:o,data:c,gas:i,gasPrice:u,maxFeePerBlobGas:f,maxFeePerGas:d,maxPriorityFeePerGas:l,nonce:p,value:y,...h}=t;if(typeof n>"u")throw new Se({docsPath:"/docs/actions/wallet/sendTransaction"});const m=n?W(n):null;try{Le(t);const E=await(async()=>{if(t.to)return t.to;if(t.to!==null&&a&&a.length>0)return await Ia({authorization:a[0]}).catch(()=>{throw new k("`to` is required. Could not infer from `authorizationList`.")})})();if((m==null?void 0:m.type)==="json-rpc"||m===null){let x;r!==null&&(x=await F(e,Xe,"getChainId")({}),Ua({currentChainId:x,chain:r}));const v=(w=(g=(b=e.chain)==null?void 0:b.formatters)==null?void 0:g.transactionRequest)==null?void 0:w.format,I=(v||_e)({...ht(h,{format:v}),accessList:s,authorizationList:a,blobs:o,chainId:x,data:c,from:m==null?void 0:m.address,gas:i,gasPrice:u,maxFeePerBlobGas:f,maxFeePerGas:d,maxPriorityFeePerGas:l,nonce:p,to:E,value:y}),z=gn.get(e.uid),$=z?"wallet_sendTransaction":"eth_sendTransaction";try{return await e.request({method:$,params:[I]},{retryCount:0})}catch(N){if(z===!1)throw N;const O=N;if(O.name==="InvalidInputRpcError"||O.name==="InvalidParamsRpcError"||O.name==="MethodNotFoundRpcError"||O.name==="MethodNotSupportedRpcError")return await e.request({method:"wallet_sendTransaction",params:[I]},{retryCount:0}).then(A=>(gn.set(e.uid,!0),A)).catch(A=>{const C=A;throw C.name==="MethodNotFoundRpcError"||C.name==="MethodNotSupportedRpcError"?(gn.set(e.uid,!1),O):C});throw O}}if((m==null?void 0:m.type)==="local"){const x=await F(e,Qt,"prepareTransactionRequest")({account:m,accessList:s,authorizationList:a,blobs:o,chain:r,data:c,gas:i,gasPrice:u,maxFeePerBlobGas:f,maxFeePerGas:d,maxPriorityFeePerGas:l,nonce:p,nonceManager:m.nonceManager,parameters:[...Ma,"sidecars"],value:y,...h,to:E}),v=(P=r==null?void 0:r.serializers)==null?void 0:P.transaction,T=await m.signTransaction(x,{serializer:v});return await F(e,Nr,"sendRawTransaction")({serializedTransaction:T})}throw(m==null?void 0:m.type)==="smart"?new $t({metaMessages:["Consider using the `sendUserOperation` Action instead."],docsPath:"/docs/actions/bundler/sendUserOperation",type:"smart"}):new $t({docsPath:"/docs/actions/wallet/sendTransaction",type:m==null?void 0:m.type})}catch(E){throw E instanceof $t?E:Va(E,{...t,account:m,chain:t.chain||void 0})}}async function Wa(e,t){const{abi:n,account:r=e.account,address:s,args:a,dataSuffix:o,functionName:c,...i}=t;if(typeof r>"u")throw new Se({docsPath:"/docs/contract/writeContract"});const u=r?W(r):null,f=oe({abi:n,args:a,functionName:c});try{return await F(e,Fr,"sendTransaction")({data:`${f}${o?o.replace("0x",""):""}`,to:s,account:u,...i})}catch(d){throw Re(d,{abi:n,address:s,args:a,docsPath:"/docs/contract/writeContract",functionName:c,sender:u==null?void 0:u.address})}}function jl({abi:e,address:t,client:n}){const r=n,[s,a]=r?"public"in r&&"wallet"in r?[r.public,r.wallet]:"public"in r?[r.public,void 0]:"wallet"in r?[void 0,r.wallet]:[r,r]:[void 0,void 0],o=s!=null,c=a!=null,i={};let u=!1,f=!1,d=!1;for(const l of e)if(l.type==="function"?l.stateMutability==="view"||l.stateMutability==="pure"?u=!0:f=!0:l.type==="event"&&(d=!0),u&&f&&d)break;return o&&(u&&(i.read=new Proxy({},{get(l,p){return(...y)=>{const{args:h,options:m}=Tt(y);return F(s,he,"readContract")({abi:e,address:t,functionName:p,args:h,...m})}}})),f&&(i.simulate=new Proxy({},{get(l,p){return(...y)=>{const{args:h,options:m}=Tt(y);return F(s,Da,"simulateContract")({abi:e,address:t,functionName:p,args:h,...m})}}})),d&&(i.createEventFilter=new Proxy({},{get(l,p){return(...y)=>{const h=e.find(g=>g.type==="event"&&g.name===p),{args:m,options:b}=wn(y,h);return F(s,gr,"createContractEventFilter")({abi:e,address:t,eventName:p,args:m,...b})}}}),i.getEvents=new Proxy({},{get(l,p){return(...y)=>{const h=e.find(g=>g.type==="event"&&g.name===p),{args:m,options:b}=wn(y,h);return F(s,Br,"getContractEvents")({abi:e,address:t,eventName:p,args:m,...b})}}}),i.watchEvent=new Proxy({},{get(l,p){return(...y)=>{const h=e.find(g=>g.type==="event"&&g.name===p),{args:m,options:b}=wn(y,h);return F(s,Ha,"watchContractEvent")({abi:e,address:t,eventName:p,args:m,...b})}}}))),c&&f&&(i.write=new Proxy({},{get(l,p){return(...y)=>{const{args:h,options:m}=Tt(y);return F(a,Wa,"writeContract")({abi:e,address:t,functionName:p,args:h,...m})}}})),(o||c)&&f&&(i.estimateGas=new Proxy({},{get(l,p){return(...y)=>{const{args:h,options:m}=Tt(y);return F(s??a,qa,"estimateContractGas")({abi:e,address:t,functionName:p,args:h,...m,account:m.account??a.account})}}})),i.address=t,i.abi=e,i}function Tt(e){const t=e.length&&Array.isArray(e[0]),n=t?e[0]:[],r=(t?e[1]:e[0])??{};return{args:n,options:r}}function wn(e,t){let n=!1;Array.isArray(e[0])?n=!0:e.length===1?n=t.inputs.some(a=>a.indexed):e.length===2&&(n=!0);const r=n?e[0]:void 0,s=(n?e[1]:e[0])??{};return{args:r,options:s}}async function Za(e,t){const{atomic:n=!1,chainId:r,receipts:s,version:a="2.0.0",...o}=await e.request({method:"wallet_getCallsStatus",params:[t.id]}),[c,i]=(()=>{const u=o.status;return u>=100&&u<200?["pending",u]:u>=200&&u<300?["success",u]:u>=300&&u<700?["failure",u]:u==="CONFIRMED"?["success",200]:u==="PENDING"?["pending",100]:[void 0,u]})();return{...o,atomic:n,chainId:r?V(r):void 0,receipts:(s==null?void 0:s.map(u=>({...u,blockNumber:j(u.blockNumber),gasUsed:j(u.gasUsed),status:Ko[u.status]})))??[],statusCode:i,status:c,version:a}}async function Cc(e,t){const{id:n,pollingInterval:r=e.pollingInterval,status:s=({statusCode:l})=>l>=200,timeout:a=6e4}=t,o=X(["waitForCallsStatus",e.uid,n]),{promise:c,resolve:i,reject:u}=kr();let f;const d=Pe(o,{resolve:i,reject:u},l=>{const p=et(async()=>{const y=h=>{clearTimeout(f),p(),h(),d()};try{const h=await Za(e,{id:n});if(!s(h))return;y(()=>l.resolve(h))}catch(h){y(()=>l.reject(h))}},{interval:r,emitOnBegin:!0});return p});return f=a?setTimeout(()=>{d(),clearTimeout(f),u(new Bc({id:n}))},a):void 0,await c}class Bc extends k{constructor({id:t}){super(`Timed out while waiting for call bundle with id "${t}" to be confirmed.`,{name:"WaitForCallsStatusTimeoutError"})}}const Kn=256;let It=Kn,At;function Ka(e=11){if(!At||It+e>Kn*2){At="",It=0;for(let t=0;t<Kn;t++)At+=(256+Math.random()*256|0).toString(16).substring(1)}return At.substring(It,It+++e)}function Rr(e){const{batch:t,cacheTime:n=e.pollingInterval??4e3,ccipRead:r,key:s="base",name:a="Base Client",pollingInterval:o=4e3,type:c="base"}=e,i=e.chain,u=e.account?W(e.account):void 0,{config:f,request:d,value:l}=e.transport({chain:i,pollingInterval:o}),p={...f,...l},y={account:u,batch:t,cacheTime:n,ccipRead:r,chain:i,key:s,name:a,pollingInterval:o,request:d,transport:p,type:c,uid:Ka()};function h(m){return b=>{const g=b(m);for(const P in y)delete g[P];const w={...m,...g};return Object.assign(w,{extend:h(w)})}}return Object.assign(y,{extend:h(y)})}function Dl(){return null}const St=new mr(8192);function kc(e,{enabled:t=!0,id:n}){if(!t||!n)return e();if(St.get(n))return St.get(n);const r=e().finally(()=>St.delete(n));return St.set(n,r),r}function Yn(e,{delay:t=100,retryCount:n=2,shouldRetry:r=()=>!0}={}){return new Promise((s,a)=>{const o=async({count:c=0}={})=>{const i=async({error:u})=>{const f=typeof t=="function"?t({count:c,error:u}):t;f&&await Dt(f),o({count:c+1})};try{const u=await e();s(u)}catch(u){if(c<n&&await r({count:c,error:u}))return i({error:u});a(u)}};o()})}function $c(e,t={}){return async(n,r={})=>{var d;const{dedupe:s=!1,methods:a,retryDelay:o=150,retryCount:c=3,uid:i}={...t,...r},{method:u}=n;if((d=a==null?void 0:a.exclude)!=null&&d.includes(u))throw new Et(new Error("method not supported"),{method:u});if(a!=null&&a.include&&!a.include.includes(u))throw new Et(new Error("method not supported"),{method:u});const f=s?ot(`${i}.${X(n)}`):void 0;return kc(()=>Yn(async()=>{try{return await e(n)}catch(l){const p=l;switch(p.code){case ws.code:throw new ws(p);case gs.code:throw new gs(p);case bs.code:throw new bs(p,{method:n.method});case ys.code:throw new ys(p);case Lt.code:throw new Lt(p);case Gt.code:throw new Gt(p);case hs.code:throw new hs(p);case ms.code:throw new ms(p);case Rn.code:throw new Rn(p);case Et.code:throw new Et(p,{method:n.method});case Fn.code:throw new Fn(p);case ps.code:throw new ps(p);case kt.code:throw new kt(p);case ls.code:throw new ls(p);case fs.code:throw new fs(p);case ds.code:throw new ds(p);case us.code:throw new us(p);case cs.code:throw new cs(p);case is.code:throw new is(p);case os.code:throw new os(p);case as.code:throw new as(p);case ss.code:throw new ss(p);case rs.code:throw new rs(p);case ns.code:throw new ns(p);case ts.code:throw new ts(p);case 5e3:throw new kt(p);default:throw l instanceof k?l:new Si(p)}}},{delay:({count:l,error:p})=>{var y;if(p&&p instanceof Fe){const h=(y=p==null?void 0:p.headers)==null?void 0:y.get("Retry-After");if(h!=null&&h.match(/\d/))return Number.parseInt(h)*1e3}return~~(1<<l)*o},retryCount:c,shouldRetry:({error:l})=>Nc(l)}),{enabled:s,id:f})}}function Nc(e){return"code"in e&&typeof e.code=="number"?e.code===-1||e.code===Fn.code||e.code===Lt.code:e instanceof Fe&&e.status?e.status===403||e.status===408||e.status===413||e.status===429||e.status===500||e.status===502||e.status===503||e.status===504:!0}function sn({key:e,methods:t,name:n,request:r,retryCount:s=3,retryDelay:a=150,timeout:o,type:c},i){const u=Ka();return{config:{key:e,methods:t,name:n,request:r,retryCount:s,retryDelay:a,timeout:o,type:c},request:$c(r,{methods:t,retryCount:s,retryDelay:a,uid:u}),value:i}}function Hl(e,t={}){const{key:n="custom",methods:r,name:s="Custom Provider",retryDelay:a}=t;return({retryCount:o})=>sn({key:n,methods:r,name:s,request:e.request.bind(e),retryCount:t.retryCount??o,retryDelay:a,type:"custom"})}function Ul(e,t={}){const{key:n="fallback",name:r="Fallback",rank:s=!1,shouldThrow:a=Fc,retryCount:o,retryDelay:c}=t;return({chain:i,pollingInterval:u=4e3,timeout:f,...d})=>{let l=e,p=()=>{};const y=sn({key:n,name:r,async request({method:h,params:m}){let b;const g=async(w=0)=>{const P=l[w]({...d,chain:i,retryCount:0,timeout:f});try{const E=await P.request({method:h,params:m});return p({method:h,params:m,response:E,transport:P,status:"success"}),E}catch(E){if(p({error:E,method:h,params:m,transport:P,status:"error"}),a(E)||w===l.length-1||(b??(b=l.slice(w+1).some(x=>{const{include:v,exclude:T}=x({chain:i}).config.methods||{};return v?v.includes(h):T?!T.includes(h):!0})),!b))throw E;return g(w+1)}};return g()},retryCount:o,retryDelay:c,type:"fallback"},{onResponse:h=>p=h,transports:l.map(h=>h({chain:i,retryCount:0}))});if(s){const h=typeof s=="object"?s:{};Rc({chain:i,interval:h.interval??u,onTransports:m=>l=m,ping:h.ping,sampleCount:h.sampleCount,timeout:h.timeout,transports:l,weights:h.weights})}return y}}function Fc(e){return!!("code"in e&&typeof e.code=="number"&&(e.code===Rn.code||e.code===kt.code||$e.nodeMessage.test(e.message)||e.code===5e3))}function Rc({chain:e,interval:t=4e3,onTransports:n,ping:r,sampleCount:s=10,timeout:a=1e3,transports:o,weights:c={}}){const{stability:i=.7,latency:u=.3}=c,f=[],d=async()=>{const l=await Promise.all(o.map(async h=>{const m=h({chain:e,retryCount:0,timeout:a}),b=Date.now();let g,w;try{await(r?r({transport:m}):m.request({method:"net_listening"})),w=1}catch{w=0}finally{g=Date.now()}return{latency:g-b,success:w}}));f.push(l),f.length>s&&f.shift();const p=Math.max(...f.map(h=>Math.max(...h.map(({latency:m})=>m)))),y=o.map((h,m)=>{const b=f.map(x=>x[m].latency),w=1-b.reduce((x,v)=>x+v,0)/b.length/p,P=f.map(x=>x[m].success),E=P.reduce((x,v)=>x+v,0)/P.length;return E===0?[0,m]:[u*w+i*E,m]}).sort((h,m)=>m[0]-h[0]);n(y.map(([,h])=>o[h])),await Dt(t),d()};d()}class Ya extends k{constructor(){super("No URL was provided to the Transport. Please provide a valid RPC URL to the Transport.",{docsPath:"/docs/clients/intro",name:"UrlRequiredError"})}}function Ja(e,{errorInstance:t=new Error("timed out"),timeout:n,signal:r}){return new Promise((s,a)=>{(async()=>{let o;try{const c=new AbortController;n>0&&(o=setTimeout(()=>{r?c.abort():a(t)},n)),s(await e({signal:(c==null?void 0:c.signal)||null}))}catch(c){(c==null?void 0:c.name)==="AbortError"&&a(t),a(c)}finally{clearTimeout(o)}})()})}function Mc(){return{current:0,take(){return this.current++},reset(){this.current=0}}}const Jn=Mc();function zc(e,t={}){return{async request(n){var d;const{body:r,onRequest:s=t.onRequest,onResponse:a=t.onResponse,timeout:o=t.timeout??1e4}=n,c={...t.fetchOptions??{},...n.fetchOptions??{}},{headers:i,method:u,signal:f}=c;try{const l=await Ja(async({signal:y})=>{const h={...c,body:Array.isArray(r)?X(r.map(w=>({jsonrpc:"2.0",id:w.id??Jn.take(),...w}))):X({jsonrpc:"2.0",id:r.id??Jn.take(),...r}),headers:{"Content-Type":"application/json",...i},method:u||"POST",signal:f||(o>0?y:null)},m=new Request(e,h),b=await(s==null?void 0:s(m,h))??{...h,url:e};return await fetch(b.url??e,b)},{errorInstance:new Mn({body:r,url:e}),timeout:o,signal:!0});a&&await a(l);let p;if((d=l.headers.get("Content-Type"))!=null&&d.startsWith("application/json"))p=await l.json();else{p=await l.text();try{p=JSON.parse(p||"{}")}catch(y){if(l.ok)throw y;p={error:p}}}if(!l.ok)throw new Fe({body:r,details:X(p.error)||l.statusText,headers:l.headers,status:l.status,url:e});return p}catch(l){throw l instanceof Fe||l instanceof Mn?l:new Fe({body:r,cause:l,url:e})}}}}function Vl(e,t={}){const{batch:n,fetchOptions:r,key:s="http",methods:a,name:o="HTTP JSON-RPC",onFetchRequest:c,onFetchResponse:i,retryDelay:u,raw:f}=t;return({chain:d,retryCount:l,timeout:p})=>{const{batchSize:y=1e3,wait:h=0}=typeof n=="object"?n:{},m=t.retryCount??l,b=p??t.timeout??1e4,g=e||(d==null?void 0:d.rpcUrls.default.http[0]);if(!g)throw new Ya;const w=zc(g,{fetchOptions:r,onRequest:c,onResponse:i,timeout:b});return sn({key:s,methods:a,name:o,async request({method:P,params:E}){const x={method:P,params:E},{schedule:v}=$r({id:g,wait:h,shouldSplitBatch($){return $.length>y},fn:$=>w.request({body:$}),sort:($,N)=>$.id-N.id}),T=async $=>n?v($):[await w.request({body:$})],[{error:I,result:z}]=await T(x);if(f)return{error:I,result:z};if(I)throw new hr({body:x,error:I,url:g});return z},retryCount:m,retryDelay:u,timeout:b,type:"http"},{fetchOptions:r,url:g})}}function Mr(e,t){var r,s,a,o,c,i;if(!(e instanceof k))return!1;const n=e.walk(u=>u instanceof zn);return n instanceof zn?!!(((r=n.data)==null?void 0:r.errorName)==="ResolverNotFound"||((s=n.data)==null?void 0:s.errorName)==="ResolverWildcardNotSupported"||((a=n.data)==null?void 0:a.errorName)==="ResolverNotContract"||((o=n.data)==null?void 0:o.errorName)==="ResolverError"||((c=n.data)==null?void 0:c.errorName)==="HttpError"||(i=n.reason)!=null&&i.includes("Wildcard on non-extended resolvers is not supported")||t==="reverse"&&n.reason===ea[50]):!1}function qc(e){const{abi:t,data:n}=e,r=_t(n,0,4),s=t.find(a=>a.type==="function"&&r===dr(dt(a)));if(!s)throw new Yo(r,{docsPath:"/docs/contract/decodeFunctionData"});return{functionName:s.name,args:"inputs"in s&&s.inputs&&s.inputs.length>0?Ht(s.inputs,_t(n,4)):void 0}}const xn="/docs/contract/encodeErrorResult";function Fs(e){const{abi:t,errorName:n,args:r}=e;let s=t[0];if(n){const i=Ke({abi:t,args:r,name:n});if(!i)throw new Kr(n,{docsPath:xn});s=i}if(s.type!=="error")throw new Kr(void 0,{docsPath:xn});const a=dt(s),o=dr(a);let c="0x";if(r&&r.length>0){if(!s.inputs)throw new Jo(s.name,{docsPath:xn});c=Ye(s.inputs,r)}return ue([o,c])}const vn="/docs/contract/encodeFunctionResult";function _c(e){const{abi:t,functionName:n,result:r}=e;let s=t[0];if(n){const o=Ke({abi:t,name:n});if(!o)throw new We(n,{docsPath:vn});s=o}if(s.type!=="function")throw new We(void 0,{docsPath:vn});if(!s.outputs)throw new ra(s.name,{docsPath:vn});const a=(()=>{if(s.outputs.length===0)return[];if(s.outputs.length===1)return[r];if(Array.isArray(r))return r;throw new Xo(r)})();return Ye(s.outputs,a)}const zr="x-batch-gateway:true";async function Oc(e){const{data:t,ccipRequest:n}=e,{args:[r]}=qc({abi:Wn,data:t}),s=[],a=[];return await Promise.all(r.map(async(o,c)=>{try{a[c]=await n(o),s[c]=!1}catch(i){s[c]=!0,a[c]=Lc(i)}})),_c({abi:Wn,functionName:"query",result:[s,a]})}function Lc(e){return e.name==="HttpRequestError"&&e.status?Fs({abi:Wn,errorName:"HttpError",args:[e.status,e.shortMessage]}):Fs({abi:[Qo],errorName:"Error",args:["shortMessage"in e?e.shortMessage:e.message]})}function Nt(e){let t=new Uint8Array(32).fill(0);if(!e)return ne(t);const n=e.split(".");for(let r=n.length-1;r>=0;r-=1){const s=ei(n[r]),a=s?Te(s):ve(Ct(n[r]),"bytes");t=ve(Ut([t,a]),"bytes")}return ne(t)}function Gc(e){return`[${e.slice(2)}]`}function an(e){const t=e.replace(/^\.|\.$/gm,"");if(t.length===0)return new Uint8Array(1);const n=new Uint8Array(Ct(t).byteLength+2);let r=0;const s=t.split(".");for(let a=0;a<s.length;a++){let o=Ct(s[a]);o.byteLength>255&&(o=Ct(Gc(ti(s[a])))),n[r]=o.length,n.set(o,r+1),r+=o.length+1}return n.byteLength!==r+1?n.slice(0,r+1):n}async function jc(e,t){const{blockNumber:n,blockTag:r,coinType:s,name:a,gatewayUrls:o,strict:c}=t,{chain:i}=e,u=(()=>{if(t.universalResolverAddress)return t.universalResolverAddress;if(!i)throw new Error("client chain not configured. universalResolverAddress is required.");return Qe({blockNumber:n,chain:i,contract:"ensUniversalResolver"})})(),f=i==null?void 0:i.ensTlds;if(f&&!f.some(d=>a.endsWith(d)))return null;try{const d=oe({abi:ks,functionName:"addr",...s!=null?{args:[Nt(a),BigInt(s)]}:{args:[Nt(a)]}}),l={address:u,abi:Oa,functionName:"resolve",args:[_(an(a)),d,o??[zr]],blockNumber:n,blockTag:r},y=await F(e,he,"readContract")(l);if(y[0]==="0x")return null;const h=Ge({abi:ks,args:s!=null?[Nt(a),BigInt(s)]:void 0,functionName:"addr",data:y[0]});return h==="0x"||Ze(h)==="0x00"?null:h}catch(d){if(c)throw d;if(Mr(d,"resolve"))return null;throw d}}class Dc extends k{constructor({data:t}){super("Unable to extract image from metadata. The metadata may be malformed or invalid.",{metaMessages:["- Metadata must be a JSON object with at least an `image`, `image_url` or `image_data` property.","",`Provided data: ${JSON.stringify(t)}`],name:"EnsAvatarInvalidMetadataError"})}}class at extends k{constructor({reason:t}){super(`ENS NFT avatar URI is invalid. ${t}`,{name:"EnsAvatarInvalidNftUriError"})}}class qr extends k{constructor({uri:t}){super(`Unable to resolve ENS avatar URI "${t}". The URI may be malformed, invalid, or does not respond with a valid image.`,{name:"EnsAvatarUriResolutionError"})}}class Hc extends k{constructor({namespace:t}){super(`ENS NFT avatar namespace "${t}" is not supported. Must be "erc721" or "erc1155".`,{name:"EnsAvatarUnsupportedNamespaceError"})}}const Uc=/(?<protocol>https?:\/\/[^\/]*|ipfs:\/|ipns:\/|ar:\/)?(?<root>\/)?(?<subpath>ipfs\/|ipns\/)?(?<target>[\w\-.]+)(?<subtarget>\/.*)?/,Vc=/^(Qm[1-9A-HJ-NP-Za-km-z]{44,}|b[A-Za-z2-7]{58,}|B[A-Z2-7]{58,}|z[1-9A-HJ-NP-Za-km-z]{48,}|F[0-9A-F]{50,})(\/(?<target>[\w\-.]+))?(?<subtarget>\/.*)?$/,Wc=/^data:([a-zA-Z\-/+]*);base64,([^"].*)/,Zc=/^data:([a-zA-Z\-/+]*)?(;[a-zA-Z0-9].*?)?(,)/;async function Kc(e){try{const t=await fetch(e,{method:"HEAD"});if(t.status===200){const n=t.headers.get("content-type");return n==null?void 0:n.startsWith("image/")}return!1}catch(t){return typeof t=="object"&&typeof t.response<"u"||!globalThis.hasOwnProperty("Image")?!1:new Promise(n=>{const r=new Image;r.onload=()=>{n(!0)},r.onerror=()=>{n(!1)},r.src=e})}}function Rs(e,t){return e?e.endsWith("/")?e.slice(0,-1):e:t}function Xa({uri:e,gatewayUrls:t}){const n=Wc.test(e);if(n)return{uri:e,isOnChain:!0,isEncoded:n};const r=Rs(t==null?void 0:t.ipfs,"https://ipfs.io"),s=Rs(t==null?void 0:t.arweave,"https://arweave.net"),a=e.match(Uc),{protocol:o,subpath:c,target:i,subtarget:u=""}=(a==null?void 0:a.groups)||{},f=o==="ipns:/"||c==="ipns/",d=o==="ipfs:/"||c==="ipfs/"||Vc.test(e);if(e.startsWith("http")&&!f&&!d){let p=e;return t!=null&&t.arweave&&(p=e.replace(/https:\/\/arweave.net/g,t==null?void 0:t.arweave)),{uri:p,isOnChain:!1,isEncoded:!1}}if((f||d)&&i)return{uri:`${r}/${f?"ipns":"ipfs"}/${i}${u}`,isOnChain:!1,isEncoded:!1};if(o==="ar:/"&&i)return{uri:`${s}/${i}${u||""}`,isOnChain:!1,isEncoded:!1};let l=e.replace(Zc,"");if(l.startsWith("<svg")&&(l=`data:image/svg+xml;base64,${btoa(l)}`),l.startsWith("data:")||l.startsWith("{"))return{uri:l,isOnChain:!0,isEncoded:!1};throw new qr({uri:e})}function Qa(e){if(typeof e!="object"||!("image"in e)&&!("image_url"in e)&&!("image_data"in e))throw new Dc({data:e});return e.image||e.image_url||e.image_data}async function Yc({gatewayUrls:e,uri:t}){try{const n=await fetch(t).then(s=>s.json());return await _r({gatewayUrls:e,uri:Qa(n)})}catch{throw new qr({uri:t})}}async function _r({gatewayUrls:e,uri:t}){const{uri:n,isOnChain:r}=Xa({uri:t,gatewayUrls:e});if(r||await Kc(n))return n;throw new qr({uri:t})}function Jc(e){let t=e;t.startsWith("did:nft:")&&(t=t.replace("did:nft:","").replace(/_/g,"/"));const[n,r,s]=t.split("/"),[a,o]=n.split(":"),[c,i]=r.split(":");if(!a||a.toLowerCase()!=="eip155")throw new at({reason:"Only EIP-155 supported"});if(!o)throw new at({reason:"Chain ID not found"});if(!i)throw new at({reason:"Contract address not found"});if(!s)throw new at({reason:"Token ID not found"});if(!c)throw new at({reason:"ERC namespace not found"});return{chainID:Number.parseInt(o),namespace:c.toLowerCase(),contractAddress:i,tokenID:s}}async function Xc(e,{nft:t}){if(t.namespace==="erc721")return he(e,{address:t.contractAddress,abi:[{name:"tokenURI",type:"function",stateMutability:"view",inputs:[{name:"tokenId",type:"uint256"}],outputs:[{name:"",type:"string"}]}],functionName:"tokenURI",args:[BigInt(t.tokenID)]});if(t.namespace==="erc1155")return he(e,{address:t.contractAddress,abi:[{name:"uri",type:"function",stateMutability:"view",inputs:[{name:"_id",type:"uint256"}],outputs:[{name:"",type:"string"}]}],functionName:"uri",args:[BigInt(t.tokenID)]});throw new Hc({namespace:t.namespace})}async function Qc(e,{gatewayUrls:t,record:n}){return/eip155:/i.test(n)?eu(e,{gatewayUrls:t,record:n}):_r({uri:n,gatewayUrls:t})}async function eu(e,{gatewayUrls:t,record:n}){const r=Jc(n),s=await Xc(e,{nft:r}),{uri:a,isOnChain:o,isEncoded:c}=Xa({uri:s,gatewayUrls:t});if(o&&(a.includes("data:application/json;base64,")||a.startsWith("{"))){const u=c?atob(a.replace("data:application/json;base64,","")):a,f=JSON.parse(u);return _r({uri:Qa(f),gatewayUrls:t})}let i=r.tokenID;return r.namespace==="erc1155"&&(i=i.replace("0x","").padStart(64,"0")),Yc({gatewayUrls:t,uri:a.replace(/(?:0x)?{id}/,i)})}async function eo(e,t){const{blockNumber:n,blockTag:r,key:s,name:a,gatewayUrls:o,strict:c}=t,{chain:i}=e,u=(()=>{if(t.universalResolverAddress)return t.universalResolverAddress;if(!i)throw new Error("client chain not configured. universalResolverAddress is required.");return Qe({blockNumber:n,chain:i,contract:"ensUniversalResolver"})})(),f=i==null?void 0:i.ensTlds;if(f&&!f.some(d=>a.endsWith(d)))return null;try{const d={address:u,abi:Oa,functionName:"resolve",args:[_(an(a)),oe({abi:Bs,functionName:"text",args:[Nt(a),s]}),o??[zr]],blockNumber:n,blockTag:r},p=await F(e,he,"readContract")(d);if(p[0]==="0x")return null;const y=Ge({abi:Bs,functionName:"text",data:p[0]});return y===""?null:y}catch(d){if(c)throw d;if(Mr(d,"resolve"))return null;throw d}}async function tu(e,{blockNumber:t,blockTag:n,assetGatewayUrls:r,name:s,gatewayUrls:a,strict:o,universalResolverAddress:c}){const i=await F(e,eo,"getEnsText")({blockNumber:t,blockTag:n,key:"avatar",name:s,universalResolverAddress:c,gatewayUrls:a,strict:o});if(!i)return null;try{return await Qc(e,{record:i,gatewayUrls:r})}catch{return null}}async function nu(e,{address:t,blockNumber:n,blockTag:r,gatewayUrls:s,strict:a,universalResolverAddress:o}){let c=o;if(!c){if(!e.chain)throw new Error("client chain not configured. universalResolverAddress is required.");c=Qe({blockNumber:n,chain:e.chain,contract:"ensUniversalResolver"})}const i=`${t.toLowerCase().substring(2)}.addr.reverse`;try{const u={address:c,abi:fc,functionName:"reverse",args:[_(an(i))],blockNumber:n,blockTag:r},f=F(e,he,"readContract"),[d,l]=s?await f({...u,args:[...u.args,s]}):await f(u);return t.toLowerCase()!==l.toLowerCase()?null:d}catch(u){if(a)throw u;if(Mr(u,"reverse"))return null;throw u}}async function ru(e,t){const{blockNumber:n,blockTag:r,name:s}=t,{chain:a}=e,o=(()=>{if(t.universalResolverAddress)return t.universalResolverAddress;if(!a)throw new Error("client chain not configured. universalResolverAddress is required.");return Qe({blockNumber:n,chain:a,contract:"ensUniversalResolver"})})(),c=a==null?void 0:a.ensTlds;if(c&&!c.some(u=>s.endsWith(u)))throw new Error(`${s} is not a valid ENS TLD (${c==null?void 0:c.join(", ")}) for chain "${a.name}" (id: ${a.id}).`);const[i]=await F(e,he,"readContract")({address:o,abi:[{inputs:[{type:"bytes"}],name:"findResolver",outputs:[{type:"address"},{type:"bytes32"}],stateMutability:"view",type:"function"}],functionName:"findResolver",args:[_(an(s))],blockNumber:n,blockTag:r});return i}async function to(e,t){var m,b,g;const{account:n=e.account,blockNumber:r,blockTag:s="latest",blobs:a,data:o,gas:c,gasPrice:i,maxFeePerBlobGas:u,maxFeePerGas:f,maxPriorityFeePerGas:d,to:l,value:p,...y}=t,h=n?W(n):void 0;try{Le(t);const P=(r?M(r):void 0)||s,E=(g=(b=(m=e.chain)==null?void 0:m.formatters)==null?void 0:b.transactionRequest)==null?void 0:g.format,v=(E||_e)({...ht(y,{format:E}),from:h==null?void 0:h.address,blobs:a,data:o,gas:c,gasPrice:i,maxFeePerBlobGas:u,maxFeePerGas:f,maxPriorityFeePerGas:d,to:l,value:p}),T=await e.request({method:"eth_createAccessList",params:[v,P]});return{accessList:T.accessList,gasUsed:BigInt(T.gasUsed)}}catch(w){throw ja(w,{...t,account:h,chain:e.chain})}}async function su(e){const t=Kt(e,{method:"eth_newBlockFilter"}),n=await e.request({method:"eth_newBlockFilter"});return{id:n,request:t(n),type:"block"}}async function no(e,{address:t,args:n,event:r,events:s,fromBlock:a,strict:o,toBlock:c}={}){const i=s??(r?[r]:void 0),u=Kt(e,{method:"eth_newFilter"});let f=[];i&&(f=[i.flatMap(p=>ut({abi:[p],eventName:p.name,args:n}))],r&&(f=f[0]));const d=await e.request({method:"eth_newFilter",params:[{address:t,fromBlock:typeof a=="bigint"?M(a):a,toBlock:typeof c=="bigint"?M(c):c,...f.length?{topics:f}:{}}]});return{abi:i,args:n,eventName:r?r.name:void 0,fromBlock:a,id:d,request:u(d),strict:!!o,toBlock:c,type:"event"}}async function ro(e){const t=Kt(e,{method:"eth_newPendingTransactionFilter"}),n=await e.request({method:"eth_newPendingTransactionFilter"});return{id:n,request:t(n),type:"transaction"}}async function au(e){const t=await e.request({method:"eth_blobBaseFee"});return BigInt(t)}async function ou(e,{blockHash:t,blockNumber:n,blockTag:r="latest"}={}){const s=n!==void 0?M(n):void 0;let a;return t?a=await e.request({method:"eth_getBlockTransactionCountByHash",params:[t]},{dedupe:!0}):a=await e.request({method:"eth_getBlockTransactionCountByNumber",params:[s||r]},{dedupe:!!s}),V(a)}async function Ms(e,{address:t,blockNumber:n,blockTag:r="latest"}){const s=n!==void 0?M(n):void 0,a=await e.request({method:"eth_getCode",params:[t,s||r]},{dedupe:!!s});if(a!=="0x")return a}class iu extends k{constructor({address:t}){super(`No EIP-712 domain found on contract "${t}".`,{metaMessages:["Ensure that:",`- The contract is deployed at the address "${t}".`,"- `eip712Domain()` function exists on the contract.","- `eip712Domain()` function matches signature to ERC-5267 specification."],name:"Eip712DomainNotFoundError"})}}async function cu(e,t){const{address:n,factory:r,factoryData:s}=t;try{const[a,o,c,i,u,f,d]=await F(e,he,"readContract")({abi:uu,address:n,functionName:"eip712Domain",factory:r,factoryData:s});return{domain:{name:o,version:c,chainId:Number(i),verifyingContract:u,salt:f},extensions:d,fields:a}}catch(a){const o=a;throw o.name==="ContractFunctionExecutionError"&&o.cause.name==="ContractFunctionZeroDataError"?new iu({address:n}):o}}const uu=[{inputs:[],name:"eip712Domain",outputs:[{name:"fields",type:"bytes1"},{name:"name",type:"string"},{name:"version",type:"string"},{name:"chainId",type:"uint256"},{name:"verifyingContract",type:"address"},{name:"salt",type:"bytes32"},{name:"extensions",type:"uint256[]"}],stateMutability:"view",type:"function"}];function du(e){var t;return{baseFeePerGas:e.baseFeePerGas.map(n=>BigInt(n)),gasUsedRatio:e.gasUsedRatio,oldestBlock:BigInt(e.oldestBlock),reward:(t=e.reward)==null?void 0:t.map(n=>n.map(r=>BigInt(r)))}}async function fu(e,{blockCount:t,blockNumber:n,blockTag:r="latest",rewardPercentiles:s}){const a=n?M(n):void 0,o=await e.request({method:"eth_feeHistory",params:[M(t),a||r,s]},{dedupe:!!a});return du(o)}async function lu(e,{filter:t}){const n=t.strict??!1,s=(await t.request({method:"eth_getFilterLogs",params:[t.id]})).map(a=>Ie(a));return t.abi?pr({abi:t.abi,logs:s,strict:n}):s}class pu extends k{constructor({callbackSelector:t,cause:n,data:r,extraData:s,sender:a,urls:o}){var c;super(n.shortMessage||"An error occurred while fetching for an offchain result.",{cause:n,metaMessages:[...n.metaMessages||[],(c=n.metaMessages)!=null&&c.length?"":[],"Offchain Gateway Call:",o&&["  Gateway URL(s):",...o.map(i=>`    ${ma(i)}`)],`  Sender: ${a}`,`  Data: ${r}`,`  Callback selector: ${t}`,`  Extra data: ${s}`].flat(),name:"OffchainLookupError"})}}class mu extends k{constructor({result:t,url:n}){super("Offchain gateway response is malformed. Response data must be a hex value.",{metaMessages:[`Gateway URL: ${ma(n)}`,`Response: ${X(t)}`],name:"OffchainLookupResponseMalformedError"})}}class hu extends k{constructor({sender:t,to:n}){super("Reverted sender address does not match target contract address (`to`).",{metaMessages:[`Contract address: ${n}`,`OffchainLookup sender address: ${t}`],name:"OffchainLookupSenderMismatchError"})}}const yu="0x556f1830",so={name:"OffchainLookup",type:"error",inputs:[{name:"sender",type:"address"},{name:"urls",type:"string[]"},{name:"callData",type:"bytes"},{name:"callbackFunction",type:"bytes4"},{name:"extraData",type:"bytes"}]};async function bu(e,{blockNumber:t,blockTag:n,data:r,to:s}){const{args:a}=Qs({data:r,abi:[so]}),[o,c,i,u,f]=a,{ccipRead:d}=e,l=d&&typeof(d==null?void 0:d.request)=="function"?d.request:ao;try{if(!Oe(s,o))throw new hu({sender:o,to:s});const p=c.includes(zr)?await Oc({data:i,ccipRequest:l}):await l({data:i,sender:o,urls:c}),{data:y}=await yt(e,{blockNumber:t,blockTag:n,data:Ut([u,Ye([{type:"bytes"},{type:"bytes"}],[p,f])]),to:s});return y}catch(p){throw new pu({callbackSelector:u,cause:p,data:r,extraData:f,sender:o,urls:c})}}async function ao({data:e,sender:t,urls:n}){var s;let r=new Error("An unknown error occurred.");for(let a=0;a<n.length;a++){const o=n[a],c=o.includes("{data}")?"GET":"POST",i=c==="POST"?{data:e,sender:t}:void 0,u=c==="POST"?{"Content-Type":"application/json"}:{};try{const f=await fetch(o.replace("{sender}",t.toLowerCase()).replace("{data}",e),{body:JSON.stringify(i),headers:u,method:c});let d;if((s=f.headers.get("Content-Type"))!=null&&s.startsWith("application/json")?d=(await f.json()).data:d=await f.text(),!f.ok){r=new Fe({body:i,details:d!=null&&d.error?X(d.error):f.statusText,headers:f.headers,status:f.status,url:o});continue}if(!G(d)){r=new mu({result:d,url:o});continue}return d}catch(f){r=new Fe({body:i,details:f.message,url:o})}}throw r}const gu=Object.freeze(Object.defineProperty({__proto__:null,ccipRequest:ao,offchainLookup:bu,offchainLookupAbiItem:so,offchainLookupSignature:yu},Symbol.toStringTag,{value:"Module"}));function Wl({chains:e,id:t}){return e.find(n=>n.id===t)}const En=new Map;async function wu(e){const{getSocket:t,keepAlive:n=!0,key:r="socket",reconnect:s=!0,url:a}=e,{interval:o=3e4}=typeof n=="object"?n:{},{attempts:c=5,delay:i=2e3}=typeof s=="object"?s:{};let u=En.get(`${r}:${a}`);if(u)return u;let f=0;const{schedule:d}=$r({id:`${r}:${a}`,fn:async()=>{const y=new Map,h=new Map;let m,b,g;async function w(){const P=await t({onClose(){var E,x;for(const v of y.values())(E=v.onError)==null||E.call(v,new jt({url:a}));for(const v of h.values())(x=v.onError)==null||x.call(v,new jt({url:a}));y.clear(),h.clear(),s&&f<c&&setTimeout(async()=>{f++,await w().catch(console.error)},i)},onError(E){var x,v;m=E;for(const T of y.values())(x=T.onError)==null||x.call(T,m);for(const T of h.values())(v=T.onError)==null||v.call(T,m);y.clear(),h.clear(),u==null||u.close(),s&&f<c&&setTimeout(async()=>{f++,await w().catch(console.error)},i)},onOpen(){m=void 0,f=0},onResponse(E){const x=E.method==="eth_subscription",v=x?E.params.subscription:E.id,T=x?h:y,I=T.get(v);I&&I.onResponse(E),x||T.delete(v)}});return b=P,n&&(g&&clearInterval(g),g=setInterval(()=>{var E;return(E=b.ping)==null?void 0:E.call(b)},o)),P}return await w(),m=void 0,u={close(){g&&clearInterval(g),b.close(),En.delete(`${r}:${a}`)},get socket(){return b},request({body:P,onError:E,onResponse:x}){m&&E&&E(m);const v=P.id??Jn.take(),T=I=>{var z;typeof I.id=="number"&&v!==I.id||(P.method==="eth_subscribe"&&typeof I.result=="string"&&h.set(I.result,{onResponse:T,onError:E}),P.method==="eth_unsubscribe"&&h.delete((z=P.params)==null?void 0:z[0]),x(I))};y.set(v,{onResponse:T,onError:E});try{b.request({body:{jsonrpc:"2.0",id:v,...P}})}catch(I){E==null||E(I)}},requestAsync({body:P,timeout:E=1e4}){return Ja(()=>new Promise((x,v)=>this.request({body:P,onError:v,onResponse:x})),{errorInstance:new Mn({body:P,url:a}),timeout:E})},requests:y,subscriptions:h,url:a},En.set(`${r}:${a}`,u),[u]}}),[l,[p]]=await d();return p}async function Ft(e,t={}){const{keepAlive:n,reconnect:r}=t;return wu({async getSocket({onClose:s,onError:a,onOpen:o,onResponse:c}){const i=await lr(()=>import("./native-CJ5et6AR.js"),[]).then(p=>p.WebSocket),u=new i(e);function f(){u.removeEventListener("close",f),u.removeEventListener("message",d),u.removeEventListener("error",a),u.removeEventListener("open",o),s()}function d({data:p}){try{const y=JSON.parse(p);c(y)}catch(y){a(y)}}u.addEventListener("close",f),u.addEventListener("message",d),u.addEventListener("error",a),u.addEventListener("open",o),u.readyState===i.CONNECTING&&await new Promise((p,y)=>{u&&(u.onopen=p,u.onerror=y)});const{close:l}=u;return Object.assign(u,{close(){l.bind(u)(),f()},ping(){try{if(u.readyState===u.CLOSED||u.readyState===u.CLOSING)throw new xs({url:u.url,cause:new jt({url:u.url})});const p={jsonrpc:"2.0",method:"net_version",params:[]};u.send(JSON.stringify(p))}catch(p){a(p)}},request({body:p}){if(u.readyState===u.CLOSED||u.readyState===u.CLOSING)throw new xs({body:p,url:u.url,cause:new jt({url:u.url})});return u.send(JSON.stringify(p))}})},keepAlive:n,reconnect:r,url:e})}async function xu(e){const t=await Ft(e);return Object.assign(t.socket,{requests:t.requests,subscriptions:t.subscriptions})}function Zl(e,t){if(e.length!==t.length)throw new ni({expectedLength:e.length,givenLength:t.length});const n=[];for(let r=0;r<e.length;r++){const s=e[r],a=t[r];n.push(oo(s,a))}return ue(n)}function oo(e,t,n=!1){if(e==="address"){const o=t;if(!pe(o))throw new me({address:o});return Bt(o.toLowerCase(),{size:n?32:null})}if(e==="string")return ot(t);if(e==="bytes")return t;if(e==="bool")return Bt(ri(t),{size:n?32:1});const r=e.match(si);if(r){const[o,c,i="256"]=r,u=Number.parseInt(i)/8;return M(t,{size:n?32:u,signed:c==="int"})}const s=e.match(ai);if(s){const[o,c]=s;if(Number.parseInt(c)!==(t.length-2)/2)throw new oi({expectedSize:Number.parseInt(c),givenSize:(t.length-2)/2});return Bt(t,{dir:"right",size:n?32:null})}const a=e.match(ii);if(a&&Array.isArray(t)){const[o,c]=a,i=[];for(let u=0;u<t.length;u++)i.push(oo(c,t[u],!0));return i.length===0?"0x":ue(i)}throw new ci(e)}function io(e){const{authorizationList:t}=e;if(t)for(const n of t){const{chainId:r}=n,s=n.address;if(!pe(s))throw new me({address:s});if(r<0)throw new en({chainId:r})}on(e)}function co(e){const{blobVersionedHashes:t}=e;if(t){if(t.length===0)throw new Fa;for(const n of t){const r=ft(n),s=V(_t(n,0,1));if(r!==32)throw new cc({hash:n,size:r});if(s!==Na)throw new uc({hash:n,version:s})}}on(e)}function on(e){const{chainId:t,maxPriorityFeePerGas:n,maxFeePerGas:r,to:s}=e;if(t<=0)throw new en({chainId:t});if(s&&!pe(s))throw new me({address:s});if(r&&r>Zt)throw new Me({maxFeePerGas:r});if(n&&r&&n>r)throw new ct({maxFeePerGas:r,maxPriorityFeePerGas:n})}function uo(e){const{chainId:t,maxPriorityFeePerGas:n,gasPrice:r,maxFeePerGas:s,to:a}=e;if(t<=0)throw new en({chainId:t});if(a&&!pe(a))throw new me({address:a});if(n||s)throw new k("`maxFeePerGas`/`maxPriorityFeePerGas` is not a valid EIP-2930 Transaction attribute.");if(r&&r>Zt)throw new Me({maxFeePerGas:r})}function fo(e){const{chainId:t,maxPriorityFeePerGas:n,gasPrice:r,maxFeePerGas:s,to:a}=e;if(a&&!pe(a))throw new me({address:a});if(typeof t<"u"&&t<=0)throw new en({chainId:t});if(n||s)throw new k("`maxFeePerGas`/`maxPriorityFeePerGas` is not a valid Legacy Transaction attribute.");if(r&&r>Zt)throw new Me({maxFeePerGas:r})}function cn(e){if(!e||e.length===0)return[];const t=[];for(let n=0;n<e.length;n++){const{address:r,storageKeys:s}=e[n];for(let a=0;a<s.length;a++)if(s[a].length-2!==64)throw new Hi({storageKey:s[a]});if(!pe(r,{strict:!1}))throw new me({address:r});t.push([r,s])}return t}function vu(e,t){const n=Ra(e);return n==="eip1559"?Tu(e,t):n==="eip2930"?Iu(e,t):n==="eip4844"?Pu(e,t):n==="eip7702"?Eu(e,t):Au(e,t)}function Eu(e,t){const{authorizationList:n,chainId:r,gas:s,nonce:a,to:o,value:c,maxFeePerGas:i,maxPriorityFeePerGas:u,accessList:f,data:d}=e;io(e);const l=cn(f),p=Su(n);return ue(["0x04",Ae([_(r),a?_(a):"0x",u?_(u):"0x",i?_(i):"0x",s?_(s):"0x",o??"0x",c?_(c):"0x",d??"0x",l,p,...gt(e,t)])])}function Pu(e,t){const{chainId:n,gas:r,nonce:s,to:a,value:o,maxFeePerBlobGas:c,maxFeePerGas:i,maxPriorityFeePerGas:u,accessList:f,data:d}=e;co(e);let l=e.blobVersionedHashes,p=e.sidecars;if(e.blobs&&(typeof l>"u"||typeof p>"u")){const w=typeof e.blobs[0]=="string"?e.blobs:e.blobs.map(x=>ne(x)),P=e.kzg,E=Pr({blobs:w,kzg:P});if(typeof l>"u"&&(l=Ba({commitments:E})),typeof p>"u"){const x=Tr({blobs:w,commitments:E,kzg:P});p=Ar({blobs:w,commitments:E,proofs:x})}}const y=cn(f),h=[_(n),s?_(s):"0x",u?_(u):"0x",i?_(i):"0x",r?_(r):"0x",a??"0x",o?_(o):"0x",d??"0x",y,c?_(c):"0x",l??[],...gt(e,t)],m=[],b=[],g=[];if(p)for(let w=0;w<p.length;w++){const{blob:P,commitment:E,proof:x}=p[w];m.push(P),b.push(E),g.push(x)}return ue(["0x03",p?Ae([h,m,b,g]):Ae(h)])}function Tu(e,t){const{chainId:n,gas:r,nonce:s,to:a,value:o,maxFeePerGas:c,maxPriorityFeePerGas:i,accessList:u,data:f}=e;on(e);const d=cn(u),l=[_(n),s?_(s):"0x",i?_(i):"0x",c?_(c):"0x",r?_(r):"0x",a??"0x",o?_(o):"0x",f??"0x",d,...gt(e,t)];return ue(["0x02",Ae(l)])}function Iu(e,t){const{chainId:n,gas:r,data:s,nonce:a,to:o,value:c,accessList:i,gasPrice:u}=e;uo(e);const f=cn(i),d=[_(n),a?_(a):"0x",u?_(u):"0x",r?_(r):"0x",o??"0x",c?_(c):"0x",s??"0x",f,...gt(e,t)];return ue(["0x01",Ae(d)])}function Au(e,t){const{chainId:n=0,gas:r,data:s,nonce:a,to:o,value:c,gasPrice:i}=e;fo(e);let u=[a?_(a):"0x",i?_(i):"0x",r?_(r):"0x",o??"0x",c?_(c):"0x",s??"0x"];if(t){const f=(()=>{if(t.v>=35n)return(t.v-35n)/2n>0?t.v:27n+(t.v===35n?0n:1n);if(n>0)return BigInt(n*2)+BigInt(35n+t.v-27n);const p=27n+(t.v===27n?0n:1n);if(t.v!==p)throw new va({v:t.v});return p})(),d=Ze(t.r),l=Ze(t.s);u=[...u,_(f),d==="0x00"?"0x":d,l==="0x00"?"0x":l]}else n>0&&(u=[...u,_(n),"0x","0x"]);return Ae(u)}function gt(e,t){const n=t??e,{v:r,yParity:s}=n;if(typeof n.r>"u")return[];if(typeof n.s>"u")return[];if(typeof r>"u"&&typeof s>"u")return[];const a=Ze(n.r),o=Ze(n.s);return[typeof s=="number"?s?_(1):"0x":r===0n?"0x":r===1n?_(1):r===27n?"0x":_(1),a==="0x00"?"0x":a,o==="0x00"?"0x":o]}function Su(e){if(!e||e.length===0)return[];const t=[];for(const n of e){const{chainId:r,nonce:s,...a}=n,o=n.address;t.push([r?_(r):"0x",o,s?_(s):"0x",...gt({},a)])}return t}function zs(e){return!e||typeof e!="object"||!("BYTES_PER_ELEMENT"in e)?!1:e.BYTES_PER_ELEMENT===1&&e.constructor.name==="Uint8Array"}function Kl(e){return e.opcode==="CREATE2"?Bu(e):Cu(e)}function Cu(e){const t=Te(Ee(e.from));let n=Te(e.nonce);return n[0]===0&&(n=new Uint8Array([])),Ee(`0x${ve(Ae([t,n],"bytes")).slice(26)}`)}function Bu(e){const t=Te(Ee(e.from)),n=Bt(zs(e.salt)?e.salt:Te(e.salt),{size:32}),r="bytecodeHash"in e?zs(e.bytecodeHash)?e.bytecodeHash:Te(e.bytecodeHash):ve(e.bytecode,"bytes");return Ee(_t(ve(Ut([Te("0xff"),t,n,r])),12))}function lo(e,t="hex"){const n=(()=>{if(typeof e=="string"){if(e.length>3&&e.length%2!==0)throw new ui(e);return ce(e)}return e})(),r=zt(n,{recursiveReadLimit:Number.POSITIVE_INFINITY});return po(r,t)}function po(e,t="hex"){if(e.bytes.length===0)return t==="hex"?ne(e.bytes):e.bytes;const n=e.readByte();if(n<128&&e.decrementPosition(1),n<192){const s=qs(e,n,128),a=e.readBytes(s);return t==="hex"?ne(a):a}const r=qs(e,n,192);return ku(e,r,t)}function qs(e,t,n){if(n===128&&t<128)return 1;if(t<=n+55)return t-n;if(t===n+55+1)return e.readUint8();if(t===n+55+2)return e.readUint16();if(t===n+55+3)return e.readUint24();if(t===n+55+4)return e.readUint32();throw new k("Invalid RLP prefix")}function ku(e,t,n){const r=e.position,s=[];for(;e.position-r<t;)s.push(po(e,n));return s}function $u(e){return G(e)&&ft(e)===32}function Yl(e,t){const n=t||"hex",r=zi(G(e,{strict:!1})?Te(e):e);return n==="bytes"?r:_(r)}const Nu=`Ethereum Signed Message:
`;function Fu(e){const t=typeof e=="string"?ot(e):typeof e.raw=="string"?e.raw:ne(e.raw),n=ot(`${Nu}${ft(t)}`);return Ut([n,t])}function Or(e,t){return ve(Fu(e),t)}async function Ru({message:e,signature:t}){return Je({hash:Or(e),signature:t})}async function Mu(e){const{domain:t,message:n,primaryType:r,signature:s,types:a}=e;return Je({hash:ua({domain:t,message:n,primaryType:r,types:a}),signature:s})}async function Jl({address:e,hash:t,signature:n}){return Oe(Ee(e),await Je({hash:t,signature:n}))}async function Xl({address:e,message:t,signature:n}){return Oe(Ee(e),await Ru({message:t,signature:n}))}async function Ql(e){const{address:t,domain:n,message:r,primaryType:s,signature:a,types:o}=e;return Oe(Ee(t),await Mu({domain:n,message:r,primaryType:s,signature:a,types:o}))}const mo="0x6492649264926492649264926492649264926492649264926492649264926492",e0="0x0000000000000000000000000000000000000000000000000000000000000000";function ho(e){return da(e,-32)===mo}function t0(e){if(!ho(e))return{signature:e};const[t,n,r]=Ht([{type:"address"},{type:"bytes"},{type:"bytes"}],e);return{address:t,data:n,signature:r}}function zu(e){const{address:t,data:n,signature:r,to:s="hex"}=e,a=ue([Ye([{type:"address"},{type:"bytes"},{type:"bytes"}],[t,n,r]),mo]);return s==="hex"?a:ce(a)}function qu(e){const t=da(e,0,1);if(t==="0x04")return"eip7702";if(t==="0x03")return"eip4844";if(t==="0x02")return"eip1559";if(t==="0x01")return"eip2930";if(t!=="0x"&&V(t)>=192)return"legacy";throw new Di({serializedType:t})}function _u(e){const t=qu(e);return t==="eip1559"?Gu(e):t==="eip2930"?ju(e):t==="eip4844"?Lu(e):t==="eip7702"?Ou(e):Du(e)}function Ou(e){const t=un(e),[n,r,s,a,o,c,i,u,f,d,l,p,y]=t;if(t.length!==10&&t.length!==13)throw new pt({attributes:{chainId:n,nonce:r,maxPriorityFeePerGas:s,maxFeePerGas:a,gas:o,to:c,value:i,data:u,accessList:f,authorizationList:d,...t.length>9?{v:l,r:p,s:y}:{}},serializedTransaction:e,type:"eip7702"});const h={chainId:V(n),type:"eip7702"};return G(c)&&c!=="0x"&&(h.to=c),G(o)&&o!=="0x"&&(h.gas=j(o)),G(u)&&u!=="0x"&&(h.data=u),G(r)&&r!=="0x"&&(h.nonce=V(r)),G(i)&&i!=="0x"&&(h.value=j(i)),G(a)&&a!=="0x"&&(h.maxFeePerGas=j(a)),G(s)&&s!=="0x"&&(h.maxPriorityFeePerGas=j(s)),f.length!==0&&f!=="0x"&&(h.accessList=dn(f)),d.length!==0&&d!=="0x"&&(h.authorizationList=Hu(d)),io(h),{...t.length===13?wt(t):void 0,...h}}function Lu(e){const t=un(e),n=t.length===4,r=n?t[0]:t,s=n?t.slice(1):[],[a,o,c,i,u,f,d,l,p,y,h,m,b,g]=r,[w,P,E]=s;if(!(r.length===11||r.length===14))throw new pt({attributes:{chainId:a,nonce:o,maxPriorityFeePerGas:c,maxFeePerGas:i,gas:u,to:f,value:d,data:l,accessList:p,...r.length>9?{v:m,r:b,s:g}:{}},serializedTransaction:e,type:"eip4844"});const x={blobVersionedHashes:h,chainId:V(a),type:"eip4844"};return G(f)&&f!=="0x"&&(x.to=f),G(u)&&u!=="0x"&&(x.gas=j(u)),G(l)&&l!=="0x"&&(x.data=l),G(o)&&o!=="0x"&&(x.nonce=V(o)),G(d)&&d!=="0x"&&(x.value=j(d)),G(y)&&y!=="0x"&&(x.maxFeePerBlobGas=j(y)),G(i)&&i!=="0x"&&(x.maxFeePerGas=j(i)),G(c)&&c!=="0x"&&(x.maxPriorityFeePerGas=j(c)),p.length!==0&&p!=="0x"&&(x.accessList=dn(p)),w&&P&&E&&(x.sidecars=Ar({blobs:w,commitments:P,proofs:E})),co(x),{...r.length===14?wt(r):void 0,...x}}function Gu(e){const t=un(e),[n,r,s,a,o,c,i,u,f,d,l,p]=t;if(!(t.length===9||t.length===12))throw new pt({attributes:{chainId:n,nonce:r,maxPriorityFeePerGas:s,maxFeePerGas:a,gas:o,to:c,value:i,data:u,accessList:f,...t.length>9?{v:d,r:l,s:p}:{}},serializedTransaction:e,type:"eip1559"});const y={chainId:V(n),type:"eip1559"};return G(c)&&c!=="0x"&&(y.to=c),G(o)&&o!=="0x"&&(y.gas=j(o)),G(u)&&u!=="0x"&&(y.data=u),G(r)&&r!=="0x"&&(y.nonce=V(r)),G(i)&&i!=="0x"&&(y.value=j(i)),G(a)&&a!=="0x"&&(y.maxFeePerGas=j(a)),G(s)&&s!=="0x"&&(y.maxPriorityFeePerGas=j(s)),f.length!==0&&f!=="0x"&&(y.accessList=dn(f)),on(y),{...t.length===12?wt(t):void 0,...y}}function ju(e){const t=un(e),[n,r,s,a,o,c,i,u,f,d,l]=t;if(!(t.length===8||t.length===11))throw new pt({attributes:{chainId:n,nonce:r,gasPrice:s,gas:a,to:o,value:c,data:i,accessList:u,...t.length>8?{v:f,r:d,s:l}:{}},serializedTransaction:e,type:"eip2930"});const p={chainId:V(n),type:"eip2930"};return G(o)&&o!=="0x"&&(p.to=o),G(a)&&a!=="0x"&&(p.gas=j(a)),G(i)&&i!=="0x"&&(p.data=i),G(r)&&r!=="0x"&&(p.nonce=V(r)),G(c)&&c!=="0x"&&(p.value=j(c)),G(s)&&s!=="0x"&&(p.gasPrice=j(s)),u.length!==0&&u!=="0x"&&(p.accessList=dn(u)),uo(p),{...t.length===11?wt(t):void 0,...p}}function Du(e){const t=lo(e,"hex"),[n,r,s,a,o,c,i,u,f]=t;if(!(t.length===6||t.length===9))throw new pt({attributes:{nonce:n,gasPrice:r,gas:s,to:a,value:o,data:c,...t.length>6?{v:i,r:u,s:f}:{}},serializedTransaction:e,type:"legacy"});const d={type:"legacy"};if(G(a)&&a!=="0x"&&(d.to=a),G(s)&&s!=="0x"&&(d.gas=j(s)),G(c)&&c!=="0x"&&(d.data=c),G(n)&&n!=="0x"&&(d.nonce=V(n)),G(o)&&o!=="0x"&&(d.value=j(o)),G(r)&&r!=="0x"&&(d.gasPrice=j(r)),fo(d),t.length===6)return d;const l=G(i)&&i!=="0x"?j(i):0n;if(f==="0x"&&u==="0x")return l>0&&(d.chainId=Number(l)),d;const p=l,y=Number((p-35n)/2n);if(y>0)d.chainId=y;else if(p!==27n&&p!==28n)throw new va({v:p});return d.v=p,d.s=f,d.r=u,d.yParity=p%2n===0n?1:0,d}function un(e){return lo(`0x${e.slice(4)}`,"hex")}function dn(e){const t=[];for(let n=0;n<e.length;n++){const[r,s]=e[n];if(!pe(r,{strict:!1}))throw new me({address:r});t.push({address:r,storageKeys:s.map(a=>$u(a)?a:Ze(a))})}return t}function Hu(e){const t=[];for(let n=0;n<e.length;n++){const[r,s,a,o,c,i]=e[n];t.push({address:s,chainId:V(r),nonce:V(a),...wt([o,c,i])})}return t}function wt(e){const t=e.slice(-3),n=t[0]==="0x"||j(t[0])===0n?27n:28n;return{r:Yr(t[1],{size:32}),s:Yr(t[2],{size:32}),v:n,yParity:n===27n?0:1}}class Uu extends k{constructor({value:t}){super(`Number \`${t}\` is not a valid decimal number.`,{name:"InvalidDecimalNumberError"})}}function yo(e,t){if(!/^(-?)([0-9]*)\.?([0-9]*)$/.test(e))throw new Uu({value:e});let[n,r="0"]=e.split(".");const s=n.startsWith("-");if(s&&(n=n.slice(1)),r=r.replace(/(0+)$/,""),t===0)Math.round(+`.${r}`)===1&&(n=`${BigInt(n)+1n}`),r="";else if(r.length>t){const[a,o,c]=[r.slice(0,t-1),r.slice(t-1,t),r.slice(t)],i=Math.round(+`${o}.${c}`);i>9?r=`${BigInt(a)+BigInt(1)}0`.padStart(a.length+1,"0"):r=`${a}${i}`,r.length>t&&(r=r.slice(1),n=`${BigInt(n)+1n}`),r=r.slice(0,t)}else r=r.padEnd(t,"0");return BigInt(`${s?"-":""}${n}${r}`)}function n0(e,t="wei"){return yo(e,ga[t])}function r0(e,t="wei"){return yo(e,wa[t])}function Vu(e){const{source:t}=e,n=new Map,r=new mr(8192),s=new Map,a=({address:o,chainId:c})=>`${o}.${c}`;return{async consume({address:o,chainId:c,client:i}){const u=a({address:o,chainId:c}),f=this.get({address:o,chainId:c,client:i});this.increment({address:o,chainId:c});const d=await f;return await t.set({address:o,chainId:c},d),r.set(u,d),d},async increment({address:o,chainId:c}){const i=a({address:o,chainId:c}),u=n.get(i)??0;n.set(i,u+1)},async get({address:o,chainId:c,client:i}){const u=a({address:o,chainId:c});let f=s.get(u);return f||(f=(async()=>{try{const l=await t.get({address:o,chainId:c,client:i}),p=r.get(u)??0;return p>0&&l<=p?p+1:(r.delete(u),l)}finally{this.reset({address:o,chainId:c})}})(),s.set(u,f)),(n.get(u)??0)+await f},reset({address:o,chainId:c}){const i=a({address:o,chainId:c});n.delete(i),s.delete(i)}}}function Wu(){return{async get(e){const{address:t,client:n}=e;return Xt(n,{address:t,blockTag:"pending"})},set(){}}}const s0=Vu({source:Wu()});function Zu(e){return e.map(t=>({...t,value:BigInt(t.value)}))}function Ku(e){return{...e,balance:e.balance?BigInt(e.balance):void 0,nonce:e.nonce?V(e.nonce):void 0,storageProof:e.storageProof?Zu(e.storageProof):void 0}}async function Yu(e,{address:t,blockNumber:n,blockTag:r,storageKeys:s}){const a=r??"latest",o=n!==void 0?M(n):void 0,c=await e.request({method:"eth_getProof",params:[t,s,o||a]});return Ku(c)}async function Ju(e,{address:t,blockNumber:n,blockTag:r="latest",slot:s}){const a=n!==void 0?M(n):void 0;return await e.request({method:"eth_getStorageAt",params:[t,s,a||r]})}async function Lr(e,{blockHash:t,blockNumber:n,blockTag:r,hash:s,index:a}){var f,d,l;const o=r||"latest",c=n!==void 0?M(n):void 0;let i=null;if(s?i=await e.request({method:"eth_getTransactionByHash",params:[s]},{dedupe:!0}):t?i=await e.request({method:"eth_getTransactionByBlockHashAndIndex",params:[t,M(a)]},{dedupe:!0}):(c||o)&&(i=await e.request({method:"eth_getTransactionByBlockNumberAndIndex",params:[c||o,M(a)]},{dedupe:!!c})),!i)throw new Ea({blockHash:t,blockNumber:n,blockTag:o,hash:s,index:a});return(((l=(d=(f=e.chain)==null?void 0:f.formatters)==null?void 0:d.transaction)==null?void 0:l.format)||di)(i)}async function Xu(e,{hash:t,transactionReceipt:n}){const[r,s]=await Promise.all([F(e,bt,"getBlockNumber")({}),t?F(e,Lr,"getTransaction")({hash:t}):void 0]),a=(n==null?void 0:n.blockNumber)||(s==null?void 0:s.blockNumber);return a?r-a+1n:0n}async function Xn(e,{hash:t}){var s,a,o;const n=await e.request({method:"eth_getTransactionReceipt",params:[t]},{dedupe:!0});if(!n)throw new Pa({hash:t});return(((o=(a=(s=e.chain)==null?void 0:s.formatters)==null?void 0:a.transactionReceipt)==null?void 0:o.format)||fi)(n)}async function Qu(e,t){var m;const{allowFailure:n=!0,batchSize:r,blockNumber:s,blockTag:a,multicallAddress:o,stateOverride:c}=t,i=t.contracts,u=r??(typeof((m=e.batch)==null?void 0:m.multicall)=="object"&&e.batch.multicall.batchSize||1024);let f=o;if(!f){if(!e.chain)throw new Error("client chain not configured. multicallAddress is required.");f=Qe({blockNumber:s,chain:e.chain,contract:"multicall3"})}const d=[[]];let l=0,p=0;for(let b=0;b<i.length;b++){const{abi:g,address:w,args:P,functionName:E}=i[b];try{const x=oe({abi:g,args:P,functionName:E});p+=(x.length-2)/2,u>0&&p>u&&d[l].length>0&&(l++,p=(x.length-2)/2,d[l]=[]),d[l]=[...d[l],{allowFailure:!0,callData:x,target:w}]}catch(x){const v=Re(x,{abi:g,address:w,args:P,docsPath:"/docs/contract/multicall",functionName:E});if(!n)throw v;d[l]=[...d[l],{allowFailure:!0,callData:"0x",target:w}]}}const y=await Promise.allSettled(d.map(b=>F(e,he,"readContract")({abi:Vn,address:f,args:[b],blockNumber:s,blockTag:a,functionName:"aggregate3",stateOverride:c}))),h=[];for(let b=0;b<y.length;b++){const g=y[b];if(g.status==="rejected"){if(!n)throw g.reason;for(let P=0;P<d[b].length;P++)h.push({status:"failure",error:g.reason,result:void 0});continue}const w=g.value;for(let P=0;P<w.length;P++){const{returnData:E,success:x}=w[P],{callData:v}=d[b][P],{abi:T,address:I,functionName:z,args:$}=i[h.length];try{if(v==="0x")throw new fr;if(!x)throw new Yt({data:E});const N=Ge({abi:T,args:$,data:E,functionName:z});h.push(n?{result:N,status:"success"}:N)}catch(N){const O=Re(N,{abi:T,address:I,args:$,docsPath:"/docs/contract/multicall",functionName:z});if(!n)throw O;h.push({error:O,result:void 0,status:"failure"})}}}if(h.length!==i.length)throw new k("multicall results mismatch");return h}const ed="0.1.1";function td(){return ed}class H extends Error{constructor(t,n={}){const r=(()=>{var i;if(n.cause instanceof H){if(n.cause.details)return n.cause.details;if(n.cause.shortMessage)return n.cause.shortMessage}return(i=n.cause)!=null&&i.message?n.cause.message:n.details})(),s=n.cause instanceof H&&n.cause.docsPath||n.docsPath,o=`https://oxlib.sh${s??""}`,c=[t||"An error occurred.",...n.metaMessages?["",...n.metaMessages]:[],...r||s?["",r?`Details: ${r}`:void 0,s?`See: ${o}`:void 0]:[]].filter(i=>typeof i=="string").join(`
`);super(c,n.cause?{cause:n.cause}:void 0),Object.defineProperty(this,"details",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"docs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"docsPath",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"shortMessage",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"cause",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"BaseError"}),Object.defineProperty(this,"version",{enumerable:!0,configurable:!0,writable:!0,value:`ox@${td()}`}),this.cause=n.cause,this.details=r,this.docs=o,this.docsPath=s,this.shortMessage=t}walk(t){return bo(this,t)}}function bo(e,t){return t!=null&&t(e)?e:e&&typeof e=="object"&&"cause"in e&&e.cause?bo(e.cause,t):t?null:e}const nd="#__bigint";function rd(e,t,n){return JSON.stringify(e,(r,s)=>typeof s=="bigint"?s.toString()+nd:s,n)}function sd(e,t){if(Os(e)>t)throw new md({givenSize:Os(e),maxSize:t})}const ge={zero:48,nine:57,A:65,F:70,a:97,f:102};function _s(e){if(e>=ge.zero&&e<=ge.nine)return e-ge.zero;if(e>=ge.A&&e<=ge.F)return e-(ge.A-10);if(e>=ge.a&&e<=ge.f)return e-(ge.a-10)}function ad(e,t={}){const{dir:n,size:r=32}=t;if(r===0)return e;if(e.length>r)throw new hd({size:e.length,targetSize:r,type:"Bytes"});const s=new Uint8Array(r);for(let a=0;a<r;a++){const o=n==="right";s[o?a:r-a-1]=e[o?a:e.length-a-1]}return s}function Gr(e,t){if(ae(e)>t)throw new xd({givenSize:ae(e),maxSize:t})}function od(e,t){if(typeof t=="number"&&t>0&&t>ae(e)-1)throw new Eo({offset:t,position:"start",size:ae(e)})}function id(e,t,n){if(typeof t=="number"&&typeof n=="number"&&ae(e)!==n-t)throw new Eo({offset:n,position:"end",size:ae(e)})}function go(e,t={}){const{dir:n,size:r=32}=t;if(r===0)return e;const s=e.replace("0x","");if(s.length>r*2)throw new vd({size:Math.ceil(s.length/2),targetSize:r,type:"Hex"});return`0x${s[n==="right"?"padEnd":"padStart"](r*2,"0")}`}const cd=new TextEncoder;function ud(e){return e instanceof Uint8Array?e:typeof e=="string"?fd(e):dd(e)}function dd(e){return e instanceof Uint8Array?e:new Uint8Array(e)}function fd(e,t={}){const{size:n}=t;let r=e;n&&(Gr(e,n),r=qe(e,n));let s=r.slice(2);s.length%2&&(s=`0${s}`);const a=s.length/2,o=new Uint8Array(a);for(let c=0,i=0;c<a;c++){const u=_s(s.charCodeAt(i++)),f=_s(s.charCodeAt(i++));if(u===void 0||f===void 0)throw new H(`Invalid byte sequence ("${s[i-2]}${s[i-1]}" in "${s}").`);o[c]=u*16+f}return o}function ld(e,t={}){const{size:n}=t,r=cd.encode(e);return typeof n=="number"?(sd(r,n),pd(r,n)):r}function pd(e,t){return ad(e,{dir:"right",size:t})}function Os(e){return e.length}let md=class extends H{constructor({givenSize:t,maxSize:n}){super(`Size cannot exceed \`${n}\` bytes. Given size: \`${t}\` bytes.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Bytes.SizeOverflowError"})}},hd=class extends H{constructor({size:t,targetSize:n,type:r}){super(`${r.charAt(0).toUpperCase()}${r.slice(1).toLowerCase()} size (\`${t}\`) exceeds padding size (\`${n}\`).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Bytes.SizeExceedsPaddingSizeError"})}};const yd=new TextEncoder,bd=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0"));function gd(e,t={}){const{strict:n=!1}=t;if(!e)throw new Ls(e);if(typeof e!="string")throw new Ls(e);if(n&&!/^0x[0-9a-fA-F]*$/.test(e))throw new Gs(e);if(!e.startsWith("0x"))throw new Gs(e)}function ye(...e){return`0x${e.reduce((t,n)=>t+n.replace("0x",""),"")}`}function wo(e,t={}){const n=`0x${Number(e)}`;return typeof t.size=="number"?(Gr(n,t.size),ze(n,t.size)):n}function xo(e,t={}){let n="";for(let s=0;s<e.length;s++)n+=bd[e[s]];const r=`0x${n}`;return typeof t.size=="number"?(Gr(r,t.size),qe(r,t.size)):r}function Q(e,t={}){const{signed:n,size:r}=t,s=BigInt(e);let a;r?n?a=(1n<<BigInt(r)*8n-1n)-1n:a=2n**(BigInt(r)*8n)-1n:typeof e=="number"&&(a=BigInt(Number.MAX_SAFE_INTEGER));const o=typeof a=="bigint"&&n?-a-1n:0;if(a&&s>a||s<o){const u=typeof e=="bigint"?"n":"";throw new vo({max:a?`${a}${u}`:void 0,min:`${o}${u}`,signed:n,size:r,value:`${e}${u}`})}const i=`0x${(n&&s<0?(1n<<BigInt(r*8))+BigInt(s):s).toString(16)}`;return r?ze(i,r):i}function jr(e,t={}){return xo(yd.encode(e),t)}function ze(e,t){return go(e,{dir:"left",size:t})}function qe(e,t){return go(e,{dir:"right",size:t})}function Dr(e,t,n,r={}){const{strict:s}=r;od(e,t);const a=`0x${e.replace("0x","").slice((t??0)*2,(n??e.length)*2)}`;return s&&id(a,t,n),a}function ae(e){return Math.ceil((e.length-2)/2)}function wd(e,t={}){const{strict:n=!1}=t;try{return gd(e,{strict:n}),!0}catch{return!1}}class vo extends H{constructor({max:t,min:n,signed:r,size:s,value:a}){super(`Number \`${a}\` is not in safe${s?` ${s*8}-bit`:""}${r?" signed":" unsigned"} integer range ${t?`(\`${n}\` to \`${t}\`)`:`(above \`${n}\`)`}`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.IntegerOutOfRangeError"})}}class Ls extends H{constructor(t){super(`Value \`${typeof t=="object"?rd(t):t}\` of type \`${typeof t}\` is an invalid hex type.`,{metaMessages:['Hex types must be represented as `"0x${string}"`.']}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.InvalidHexTypeError"})}}class Gs extends H{constructor(t){super(`Value \`${t}\` is an invalid hex value.`,{metaMessages:['Hex values must start with `"0x"` and contain only hexadecimal characters (0-9, a-f, A-F).']}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.InvalidHexValueError"})}}class xd extends H{constructor({givenSize:t,maxSize:n}){super(`Size cannot exceed \`${n}\` bytes. Given size: \`${t}\` bytes.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.SizeOverflowError"})}}class Eo extends H{constructor({offset:t,position:n,size:r}){super(`Slice ${n==="start"?"starting":"ending"} at offset \`${t}\` is out-of-bounds (size: \`${r}\`).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.SliceOffsetOutOfBoundsError"})}}class vd extends H{constructor({size:t,targetSize:n,type:r}){super(`${r.charAt(0).toUpperCase()}${r.slice(1).toLowerCase()} size (\`${t}\`) exceeds padding size (\`${n}\`).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.SizeExceedsPaddingSizeError"})}}function Ed(e){return{address:e.address,amount:Q(e.amount),index:Q(e.index),validatorIndex:Q(e.validatorIndex)}}function Pd(e){return{...typeof e.baseFeePerGas=="bigint"&&{baseFeePerGas:Q(e.baseFeePerGas)},...typeof e.blobBaseFee=="bigint"&&{blobBaseFee:Q(e.blobBaseFee)},...typeof e.feeRecipient=="string"&&{feeRecipient:e.feeRecipient},...typeof e.gasLimit=="bigint"&&{gasLimit:Q(e.gasLimit)},...typeof e.number=="bigint"&&{number:Q(e.number)},...typeof e.prevRandao=="bigint"&&{prevRandao:Q(e.prevRandao)},...typeof e.time=="bigint"&&{time:Q(e.time)},...e.withdrawals&&{withdrawals:e.withdrawals.map(Ed)}}}async function Qn(e,t){const{blockNumber:n,blockTag:r="latest",blocks:s,returnFullTransactions:a,traceTransfers:o,validation:c}=t;try{const i=[];for(const l of s){const p=l.blockOverrides?Pd(l.blockOverrides):void 0,y=l.calls.map(m=>{const b=m,g=b.account?W(b.account):void 0,w={...b,data:b.abi?oe(b):b.data,from:b.from??(g==null?void 0:g.address)};return Le(w),_e(w)}),h=l.stateOverrides?xr(l.stateOverrides):void 0;i.push({blockOverrides:p,calls:y,stateOverrides:h})}const f=(n?M(n):void 0)||r;return(await e.request({method:"eth_simulateV1",params:[{blockStateCalls:i,returnFullTransactions:a,traceTransfers:o,validation:c},f]})).map((l,p)=>({...na(l),calls:l.calls.map((y,h)=>{var z,$;const{abi:m,args:b,functionName:g,to:w}=s[p].calls[h],P=((z=y.error)==null?void 0:z.data)??y.returnData,E=BigInt(y.gasUsed),x=($=y.logs)==null?void 0:$.map(N=>Ie(N)),v=y.status==="0x1"?"success":"failure",T=m&&v==="success"&&P!=="0x"?Ge({abi:m,data:P,functionName:g}):null,I=(()=>{var O;if(v==="success")return;let N;if(((O=y.error)==null?void 0:O.data)==="0x"?N=new fr:y.error&&(N=new Yt(y.error)),!!N)return Re(N,{abi:m??[],address:w,args:b,functionName:g??"<unknown>"})})();return{data:P,gasUsed:E,logs:x,status:v,...v==="success"?{result:T}:{error:I}}})}))}catch(i){const u=i,f=Jt(u,{});throw f instanceof mt?u:f}}function Po(e,t={}){const{as:n=typeof e=="string"?"Hex":"Bytes"}=t,r=li(ud(e));return n==="Bytes"?r:xo(r)}class Td extends Map{constructor(t){super(),Object.defineProperty(this,"maxSize",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxSize=t}get(t){const n=super.get(t);return super.has(t)&&n!==void 0&&(this.delete(t),super.set(t,n)),n}set(t,n){if(super.set(t,n),this.maxSize&&this.size>this.maxSize){const r=this.keys().next().value;r&&this.delete(r)}return this}}const Id={checksum:new Td(8192)},Pn=Id.checksum,Ad=/^0x[a-fA-F0-9]{40}$/;function fn(e,t={}){const{strict:n=!0}=t;if(!Ad.test(e))throw new js({address:e,cause:new Cd});if(n){if(e.toLowerCase()===e)return;if(Sd(e)!==e)throw new js({address:e,cause:new Bd})}}function Sd(e){if(Pn.has(e))return Pn.get(e);fn(e,{strict:!1});const t=e.substring(2).toLowerCase(),n=Po(ld(t),{as:"Bytes"}),r=t.split("");for(let a=0;a<40;a+=2)n[a>>1]>>4>=8&&r[a]&&(r[a]=r[a].toUpperCase()),(n[a>>1]&15)>=8&&r[a+1]&&(r[a+1]=r[a+1].toUpperCase());const s=`0x${r.join("")}`;return Pn.set(e,s),s}function er(e,t={}){const{strict:n=!0}=t??{};try{return fn(e,{strict:n}),!0}catch{return!1}}class js extends H{constructor({address:t,cause:n}){super(`Address "${t}" is invalid.`,{cause:n}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Address.InvalidAddressError"})}}class Cd extends H{constructor(){super("Address is not a 20 byte (40 hexadecimal character) value."),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Address.InvalidInputError"})}}class Bd extends H{constructor(){super("Address does not match its checksum counterpart."),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Address.InvalidChecksumError"})}}function tr(e){let t=!0,n="",r=0,s="",a=!1;for(let o=0;o<e.length;o++){const c=e[o];if(["(",")",","].includes(c)&&(t=!0),c==="("&&r++,c===")"&&r--,!!t){if(r===0){if(c===" "&&["event","function","error",""].includes(s))s="";else if(s+=c,c===")"){a=!0;break}continue}if(c===" "){e[o-1]!==","&&n!==","&&n!==",("&&(n="",t=!1);continue}s+=c,n+=c}}if(!a)throw new H("Unable to normalize signature.");return s}function nr(e,t){const n=typeof e,r=t.type;switch(r){case"address":return er(e,{strict:!1});case"bool":return n==="boolean";case"function":return n==="string";case"string":return n==="string";default:return r==="tuple"&&"components"in t?Object.values(t.components).every((s,a)=>nr(Object.values(e)[a],s)):/^u?int(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/.test(r)?n==="number"||n==="bigint":/^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/.test(r)?n==="string"||e instanceof Uint8Array:/[a-z]+[1-9]{0,3}(\[[0-9]{0,}\])+$/.test(r)?Array.isArray(e)&&e.every(s=>nr(s,{...t,type:r.replace(/(\[[0-9]{0,}\])$/,"")})):!1}}function To(e,t,n){for(const r in e){const s=e[r],a=t[r];if(s.type==="tuple"&&a.type==="tuple"&&"components"in s&&"components"in a)return To(s.components,a.components,n[r]);const o=[s.type,a.type];if(o.includes("address")&&o.includes("bytes20")?!0:o.includes("address")&&o.includes("string")?er(n[r],{strict:!1}):o.includes("address")&&o.includes("bytes")?er(n[r],{strict:!1}):!1)return o}}function Io(e,t={}){const{prepare:n=!0}=t,r=Array.isArray(e)?Jr(e):typeof e=="string"?Jr(e):e;return{...r,...n?{hash:Ue(r)}:{}}}function kd(e,t,n){const{args:r=[],prepare:s=!0}=n??{},a=wd(t,{strict:!1}),o=e.filter(u=>a?u.type==="function"||u.type==="error"?Ao(u)===Dr(t,0,4):u.type==="event"?Ue(u)===t:!1:"name"in u&&u.name===t);if(o.length===0)throw new rr({name:t});if(o.length===1)return{...o[0],...s?{hash:Ue(o[0])}:{}};let c;for(const u of o){if(!("inputs"in u))continue;if(!r||r.length===0){if(!u.inputs||u.inputs.length===0)return{...u,...s?{hash:Ue(u)}:{}};continue}if(!u.inputs||u.inputs.length===0||u.inputs.length!==r.length)continue;if(r.every((d,l)=>{const p="inputs"in u&&u.inputs[l];return p?nr(d,p):!1})){if(c&&"inputs"in c&&c.inputs){const d=To(u.inputs,c.inputs,r);if(d)throw new Nd({abiItem:u,type:d[0]},{abiItem:c,type:d[1]})}c=u}}const i=(()=>{if(c)return c;const[u,...f]=o;return{...u,overloads:f}})();if(!i)throw new rr({name:t});return{...i,...s?{hash:Ue(i)}:{}}}function Ao(e){return Dr(Ue(e),0,4)}function $d(e){const t=typeof e=="string"?e:Bn(e);return tr(t)}function Ue(e){return typeof e!="string"&&"hash"in e&&e.hash?e.hash:Po(jr($d(e)))}class Nd extends H{constructor(t,n){super("Found ambiguous types in overloaded ABI Items.",{metaMessages:[`\`${t.type}\` in \`${tr(Bn(t.abiItem))}\`, and`,`\`${n.type}\` in \`${tr(Bn(n.abiItem))}\``,"","These types encode differently and cannot be distinguished at runtime.","Remove one of the ambiguous items in the ABI."]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiItem.AmbiguityError"})}}class rr extends H{constructor({name:t,data:n,type:r="item"}){const s=t?` with name "${t}"`:n?` with data "${n}"`:"";super(`ABI ${r}${s} not found.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiItem.NotFoundError"})}}const Fd=/^(.*)\[([0-9]*)\]$/,Rd=/^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/,So=/^(u?int)(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/;function Md({checksumAddress:e,parameters:t,values:n}){const r=[];for(let s=0;s<t.length;s++)r.push(Hr({checksumAddress:e,parameter:t[s],value:n[s]}));return r}function Hr({checksumAddress:e=!1,parameter:t,value:n}){const r=t,s=Dd(r.type);if(s){const[a,o]=s;return qd(n,{checksumAddress:e,length:a,parameter:{...r,type:o}})}if(r.type==="tuple")return jd(n,{checksumAddress:e,parameter:r});if(r.type==="address")return zd(n,{checksum:e});if(r.type==="bool")return Od(n);if(r.type.startsWith("uint")||r.type.startsWith("int")){const a=r.type.startsWith("int"),[,,o="256"]=So.exec(r.type)??[];return Ld(n,{signed:a,size:Number(o)})}if(r.type.startsWith("bytes"))return _d(n,{type:r.type});if(r.type==="string")return Gd(n);throw new $o(r.type)}function Ur(e){let t=0;for(let a=0;a<e.length;a++){const{dynamic:o,encoded:c}=e[a];o?t+=32:t+=ae(c)}const n=[],r=[];let s=0;for(let a=0;a<e.length;a++){const{dynamic:o,encoded:c}=e[a];o?(n.push(Q(t+s,{size:32})),r.push(c),s+=ae(c)):n.push(c)}return ye(...n,...r)}function zd(e,t){const{checksum:n=!1}=t;return fn(e,{strict:n}),{dynamic:!1,encoded:ze(e.toLowerCase())}}function qd(e,t){const{checksumAddress:n,length:r,parameter:s}=t,a=r===null;if(!Array.isArray(e))throw new Ud(e);if(!a&&e.length!==r)throw new Hd({expectedLength:r,givenLength:e.length,type:`${s.type}[${r}]`});let o=!1;const c=[];for(let i=0;i<e.length;i++){const u=Hr({checksumAddress:n,parameter:s,value:e[i]});u.dynamic&&(o=!0),c.push(u)}if(a||o){const i=Ur(c);if(a){const u=Q(c.length,{size:32});return{dynamic:!0,encoded:c.length>0?ye(u,i):u}}if(o)return{dynamic:!0,encoded:i}}return{dynamic:!1,encoded:ye(...c.map(({encoded:i})=>i))}}function _d(e,{type:t}){const[,n]=t.split("bytes"),r=ae(e);if(!n){let s=e;return r%32!==0&&(s=qe(s,Math.ceil((e.length-2)/2/32)*32)),{dynamic:!0,encoded:ye(ze(Q(r,{size:32})),s)}}if(r!==Number.parseInt(n))throw new Bo({expectedSize:Number.parseInt(n),value:e});return{dynamic:!1,encoded:qe(e)}}function Od(e){if(typeof e!="boolean")throw new H(`Invalid boolean value: "${e}" (type: ${typeof e}). Expected: \`true\` or \`false\`.`);return{dynamic:!1,encoded:ze(wo(e))}}function Ld(e,{signed:t,size:n}){if(typeof n=="number"){const r=2n**(BigInt(n)-(t?1n:0n))-1n,s=t?-r-1n:0n;if(e>r||e<s)throw new vo({max:r.toString(),min:s.toString(),signed:t,size:n/8,value:e.toString()})}return{dynamic:!1,encoded:Q(e,{size:32,signed:t})}}function Gd(e){const t=jr(e),n=Math.ceil(ae(t)/32),r=[];for(let s=0;s<n;s++)r.push(qe(Dr(t,s*32,(s+1)*32)));return{dynamic:!0,encoded:ye(qe(Q(ae(t),{size:32})),...r)}}function jd(e,t){const{checksumAddress:n,parameter:r}=t;let s=!1;const a=[];for(let o=0;o<r.components.length;o++){const c=r.components[o],i=Array.isArray(e)?o:c.name,u=Hr({checksumAddress:n,parameter:c,value:e[i]});a.push(u),u.dynamic&&(s=!0)}return{dynamic:s,encoded:s?Ur(a):ye(...a.map(({encoded:o})=>o))}}function Dd(e){const t=e.match(/^(.*)\[(\d+)?\]$/);return t?[t[2]?Number(t[2]):null,t[1]]:void 0}function Co(e,t,n){const{checksumAddress:r=!1}={};if(e.length!==t.length)throw new ko({expectedLength:e.length,givenLength:t.length});const s=Md({checksumAddress:r,parameters:e,values:t}),a=Ur(s);return a.length===0?"0x":a}function sr(e,t){if(e.length!==t.length)throw new ko({expectedLength:e.length,givenLength:t.length});const n=[];for(let r=0;r<e.length;r++){const s=e[r],a=t[r];n.push(sr.encode(s,a))}return ye(...n)}(function(e){function t(n,r,s=!1){if(n==="address"){const i=r;return fn(i),ze(i.toLowerCase(),s?32:0)}if(n==="string")return jr(r);if(n==="bytes")return r;if(n==="bool")return ze(wo(r),s?32:1);const a=n.match(So);if(a){const[i,u,f="256"]=a,d=Number.parseInt(f)/8;return Q(r,{size:s?32:d,signed:u==="int"})}const o=n.match(Rd);if(o){const[i,u]=o;if(Number.parseInt(u)!==(r.length-2)/2)throw new Bo({expectedSize:Number.parseInt(u),value:r});return qe(r,s?32:0)}const c=n.match(Fd);if(c&&Array.isArray(r)){const[i,u]=c,f=[];for(let d=0;d<r.length;d++)f.push(t(u,r[d],!0));return f.length===0?"0x":ye(...f)}throw new $o(n)}e.encode=t})(sr||(sr={}));class Hd extends H{constructor({expectedLength:t,givenLength:n,type:r}){super(`Array length mismatch for type \`${r}\`. Expected: \`${t}\`. Given: \`${n}\`.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.ArrayLengthMismatchError"})}}class Bo extends H{constructor({expectedSize:t,value:n}){super(`Size of bytes "${n}" (bytes${ae(n)}) does not match expected size (bytes${t}).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.BytesSizeMismatchError"})}}class ko extends H{constructor({expectedLength:t,givenLength:n}){super(["ABI encoding parameters/values length mismatch.",`Expected length (parameters): ${t}`,`Given length (values): ${n}`].join(`
`)),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.LengthMismatchError"})}}class Ud extends H{constructor(t){super(`Value \`${t}\` is not a valid array.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.InvalidArrayError"})}}class $o extends H{constructor(t){super(`Type \`${t}\` is not a valid ABI Type.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.InvalidTypeError"})}}function Vd(e,t){var s;const{bytecode:n,args:r}=t;return ye(n,(s=e.inputs)!=null&&s.length&&(r!=null&&r.length)?Co(e.inputs,r):"0x")}function Wd(e){return Io(e)}function Zd(e,...t){const{overloads:n}=e,r=n?Kd([e,...n],e.name,{args:t[0]}):e,s=Yd(r),a=t.length>0?Co(r.inputs,t[0]):void 0;return a?ye(s,a):s}function He(e,t={}){return Io(e,t)}function Kd(e,t,n){const r=kd(e,t,n);if(r.type!=="function")throw new rr({name:t,type:"function"});return r}function Yd(e){return Ao(e)}const Jd="0xeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee",fe="0x0000000000000000000000000000000000000000",Xd="0x6080604052348015600e575f80fd5b5061016d8061001c5f395ff3fe608060405234801561000f575f80fd5b5060043610610029575f3560e01c8063f8b2cb4f1461002d575b5f80fd5b610047600480360381019061004291906100db565b61005d565b604051610054919061011e565b60405180910390f35b5f8173ffffffffffffffffffffffffffffffffffffffff16319050919050565b5f80fd5b5f73ffffffffffffffffffffffffffffffffffffffff82169050919050565b5f6100aa82610081565b9050919050565b6100ba816100a0565b81146100c4575f80fd5b50565b5f813590506100d5816100b1565b92915050565b5f602082840312156100f0576100ef61007d565b5b5f6100fd848285016100c7565b91505092915050565b5f819050919050565b61011881610106565b82525050565b5f6020820190506101315f83018461010f565b9291505056fea26469706673582212203b9fe929fe995c7cf9887f0bdba8a36dd78e8b73f149b17d2d9ad7cd09d2dc6264736f6c634300081a0033";async function Qd(e,t){const{blockNumber:n,blockTag:r,calls:s,stateOverrides:a,traceAssetChanges:o,traceTransfers:c,validation:i}=t,u=t.account?W(t.account):void 0;if(o&&!u)throw new k("`account` is required when `traceAssetChanges` is true");const f=u?Vd(Wd("constructor(bytes, bytes)"),{bytecode:La,args:[Xd,Zd(He("function getBalance(address)"),[u.address])]}):void 0,d=o?await Promise.all(t.calls.map(async S=>{if(!S.data&&!S.abi)return;const{accessList:L}=await to(e,{account:u.address,...S,data:S.abi?oe(S):S.data});return L.map(({address:D,storageKeys:Z})=>Z.length>0?D:null)})).then(S=>S.flat().filter(Boolean)):[],l=a==null?void 0:a.map(S=>S.address===(u==null?void 0:u.address)?{...S,nonce:0}:S),p=await Qn(e,{blockNumber:n,blockTag:r,blocks:[...o?[{calls:[{data:f}],stateOverrides:a},{calls:d.map((S,L)=>({abi:[He("function balanceOf(address) returns (uint256)")],functionName:"balanceOf",args:[u.address],to:S,from:fe,nonce:L})),stateOverrides:[{address:fe,nonce:0}]}]:[],{calls:[...s,{}].map((S,L)=>({...S,from:u==null?void 0:u.address,nonce:L})),stateOverrides:l},...o?[{calls:[{data:f}]},{calls:d.map((S,L)=>({abi:[He("function balanceOf(address) returns (uint256)")],functionName:"balanceOf",args:[u.address],to:S,from:fe,nonce:L})),stateOverrides:[{address:fe,nonce:0}]},{calls:d.map((S,L)=>({to:S,abi:[He("function decimals() returns (uint256)")],functionName:"decimals",from:fe,nonce:L})),stateOverrides:[{address:fe,nonce:0}]},{calls:d.map((S,L)=>({to:S,abi:[He("function tokenURI(uint256) returns (string)")],functionName:"tokenURI",args:[0n],from:fe,nonce:L})),stateOverrides:[{address:fe,nonce:0}]},{calls:d.map((S,L)=>({to:S,abi:[He("function symbol() returns (string)")],functionName:"symbol",from:fe,nonce:L})),stateOverrides:[{address:fe,nonce:0}]}]:[]],traceTransfers:c,validation:i}),y=o?p[2]:p[0],[h,m,,b,g,w,P,E]=o?p:[],{calls:x,...v}=y,T=x.slice(0,-1)??[],I=(h==null?void 0:h.calls)??[],z=(m==null?void 0:m.calls)??[],$=[...I,...z].map(S=>S.status==="success"?j(S.data):null),N=(b==null?void 0:b.calls)??[],O=(g==null?void 0:g.calls)??[],A=[...N,...O].map(S=>S.status==="success"?j(S.data):null),C=((w==null?void 0:w.calls)??[]).map(S=>S.status==="success"?S.result:null),B=((E==null?void 0:E.calls)??[]).map(S=>S.status==="success"?S.result:null),R=((P==null?void 0:P.calls)??[]).map(S=>S.status==="success"?S.result:null),q=[];for(const[S,L]of A.entries()){const D=$[S];if(typeof L!="bigint"||typeof D!="bigint")continue;const Z=C[S-1],re=B[S-1],J=R[S-1],be=S===0?{address:Jd,decimals:18,symbol:"ETH"}:{address:d[S-1],decimals:J||Z?Number(Z??1):void 0,symbol:re??void 0};q.some(de=>de.token.address===be.address)||q.push({token:be,value:{pre:D,post:L,diff:L-D}})}return{assetChanges:q,block:v,results:T}}class No extends pi{constructor(t,n){super(),this.finished=!1,this.destroyed=!1,mi(t);const r=hi(n);if(this.iHash=t.create(),typeof this.iHash.update!="function")throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const s=this.blockLen,a=new Uint8Array(s);a.set(r.length>s?t.create().update(r).digest():r);for(let o=0;o<a.length;o++)a[o]^=54;this.iHash.update(a),this.oHash=t.create();for(let o=0;o<a.length;o++)a[o]^=106;this.oHash.update(a),a.fill(0)}update(t){return Xr(this),this.iHash.update(t),this}digestInto(t){Xr(this),yi(t,this.outputLen),this.finished=!0,this.iHash.digestInto(t),this.oHash.update(t),this.oHash.digestInto(t),this.destroy()}digest(){const t=new Uint8Array(this.oHash.outputLen);return this.digestInto(t),t}_cloneInto(t){t||(t=Object.create(Object.getPrototypeOf(this),{}));const{oHash:n,iHash:r,finished:s,destroyed:a,blockLen:o,outputLen:c}=this;return t=t,t.finished=s,t.destroyed=a,t.blockLen=o,t.outputLen=c,t.oHash=n._cloneInto(t.oHash),t.iHash=r._cloneInto(t.iHash),t}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}const Fo=(e,t,n)=>new No(e,t).update(n).digest();Fo.create=(e,t)=>new No(e,t);/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Y=BigInt(0),U=BigInt(1),ke=BigInt(2),ef=BigInt(3),ar=BigInt(4),Ds=BigInt(5),Hs=BigInt(8);function ee(e,t){const n=e%t;return n>=Y?n:t+n}function tf(e,t,n){if(t<Y)throw new Error("invalid exponent, negatives unsupported");if(n<=Y)throw new Error("invalid modulus");if(n===U)return Y;let r=U;for(;t>Y;)t&U&&(r=r*e%n),e=e*e%n,t>>=U;return r}function se(e,t,n){let r=e;for(;t-- >Y;)r*=r,r%=n;return r}function or(e,t){if(e===Y)throw new Error("invert: expected non-zero number");if(t<=Y)throw new Error("invert: expected positive modulus, got "+t);let n=ee(e,t),r=t,s=Y,a=U;for(;n!==Y;){const c=r/n,i=r%n,u=s-a*c;r=n,n=i,s=a,a=u}if(r!==U)throw new Error("invert: does not exist");return ee(s,t)}function nf(e){const t=(e-U)/ke;let n,r,s;for(n=e-U,r=0;n%ke===Y;n/=ke,r++);for(s=ke;s<e&&tf(s,t,e)!==e-U;s++)if(s>1e3)throw new Error("Cannot find square root: likely non-prime P");if(r===1){const o=(e+U)/ar;return function(i,u){const f=i.pow(u,o);if(!i.eql(i.sqr(f),u))throw new Error("Cannot find square root");return f}}const a=(n+U)/ke;return function(c,i){if(c.pow(i,t)===c.neg(c.ONE))throw new Error("Cannot find square root");let u=r,f=c.pow(c.mul(c.ONE,s),n),d=c.pow(i,a),l=c.pow(i,n);for(;!c.eql(l,c.ONE);){if(c.eql(l,c.ZERO))return c.ZERO;let p=1;for(let h=c.sqr(l);p<u&&!c.eql(h,c.ONE);p++)h=c.sqr(h);const y=c.pow(f,U<<BigInt(u-p-1));f=c.sqr(y),d=c.mul(d,y),l=c.mul(l,f),u=p}return d}}function rf(e){if(e%ar===ef){const t=(e+U)/ar;return function(r,s){const a=r.pow(s,t);if(!r.eql(r.sqr(a),s))throw new Error("Cannot find square root");return a}}if(e%Hs===Ds){const t=(e-Ds)/Hs;return function(r,s){const a=r.mul(s,ke),o=r.pow(a,t),c=r.mul(s,o),i=r.mul(r.mul(c,ke),o),u=r.mul(c,r.sub(i,r.ONE));if(!r.eql(r.sqr(u),s))throw new Error("Cannot find square root");return u}}return nf(e)}const sf=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function af(e){const t={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},n=sf.reduce((r,s)=>(r[s]="function",r),t);return Wt(e,n)}function of(e,t,n){if(n<Y)throw new Error("invalid exponent, negatives unsupported");if(n===Y)return e.ONE;if(n===U)return t;let r=e.ONE,s=t;for(;n>Y;)n&U&&(r=e.mul(r,s)),s=e.sqr(s),n>>=U;return r}function cf(e,t){const n=new Array(t.length),r=t.reduce((a,o,c)=>e.is0(o)?a:(n[c]=a,e.mul(a,o)),e.ONE),s=e.inv(r);return t.reduceRight((a,o,c)=>e.is0(o)?a:(n[c]=e.mul(a,n[c]),e.mul(a,o)),s),n}function Ro(e,t){const n=t!==void 0?t:e.toString(2).length,r=Math.ceil(n/8);return{nBitLength:n,nByteLength:r}}function Mo(e,t,n=!1,r={}){if(e<=Y)throw new Error("invalid field: expected ORDER > 0, got "+e);const{nBitLength:s,nByteLength:a}=Ro(e,t);if(a>2048)throw new Error("invalid field: expected ORDER of <= 2048 bytes");let o;const c=Object.freeze({ORDER:e,isLE:n,BITS:s,BYTES:a,MASK:Vt(s),ZERO:Y,ONE:U,create:i=>ee(i,e),isValid:i=>{if(typeof i!="bigint")throw new Error("invalid field element: expected bigint, got "+typeof i);return Y<=i&&i<e},is0:i=>i===Y,isOdd:i=>(i&U)===U,neg:i=>ee(-i,e),eql:(i,u)=>i===u,sqr:i=>ee(i*i,e),add:(i,u)=>ee(i+u,e),sub:(i,u)=>ee(i-u,e),mul:(i,u)=>ee(i*u,e),pow:(i,u)=>of(c,i,u),div:(i,u)=>ee(i*or(u,e),e),sqrN:i=>i*i,addN:(i,u)=>i+u,subN:(i,u)=>i-u,mulN:(i,u)=>i*u,inv:i=>or(i,e),sqrt:r.sqrt||(i=>(o||(o=rf(e)),o(c,i))),invertBatch:i=>cf(c,i),cmov:(i,u,f)=>f?u:i,toBytes:i=>n?fa(i,a):Ot(i,a),fromBytes:i=>{if(i.length!==a)throw new Error("Field.fromBytes: expected "+a+" bytes, got "+i.length);return n?la(i):Ne(i)}});return Object.freeze(c)}function zo(e){if(typeof e!="bigint")throw new Error("field order must be bigint");const t=e.toString(2).length;return Math.ceil(t/8)}function qo(e){const t=zo(e);return t+Math.ceil(t/2)}function uf(e,t,n=!1){const r=e.length,s=zo(t),a=qo(t);if(r<16||r<a||r>1024)throw new Error("expected "+a+"-1024 bytes of input, got "+r);const o=n?la(e):Ne(e),c=ee(o,t-U)+U;return n?fa(c,s):Ot(c,s)}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Us=BigInt(0),ir=BigInt(1);function Tn(e,t){const n=t.negate();return e?n:t}function _o(e,t){if(!Number.isSafeInteger(e)||e<=0||e>t)throw new Error("invalid window size, expected [1.."+t+"], got W="+e)}function In(e,t){_o(e,t);const n=Math.ceil(t/e)+1,r=2**(e-1),s=2**e,a=Vt(e),o=BigInt(e);return{windows:n,windowSize:r,mask:a,maxNumber:s,shiftBy:o}}function Vs(e,t,n){const{windowSize:r,mask:s,maxNumber:a,shiftBy:o}=n;let c=Number(e&s),i=e>>o;c>r&&(c-=a,i+=ir);const u=t*r,f=u+Math.abs(c)-1,d=c===0,l=c<0,p=t%2!==0;return{nextN:i,offset:f,isZero:d,isNeg:l,isNegF:p,offsetF:u}}function df(e,t){if(!Array.isArray(e))throw new Error("array expected");e.forEach((n,r)=>{if(!(n instanceof t))throw new Error("invalid point at index "+r)})}function ff(e,t){if(!Array.isArray(e))throw new Error("array of scalars expected");e.forEach((n,r)=>{if(!t.isValid(n))throw new Error("invalid scalar at index "+r)})}const An=new WeakMap,Oo=new WeakMap;function Sn(e){return Oo.get(e)||1}function lf(e,t){return{constTimeNegate:Tn,hasPrecomputes(n){return Sn(n)!==1},unsafeLadder(n,r,s=e.ZERO){let a=n;for(;r>Us;)r&ir&&(s=s.add(a)),a=a.double(),r>>=ir;return s},precomputeWindow(n,r){const{windows:s,windowSize:a}=In(r,t),o=[];let c=n,i=c;for(let u=0;u<s;u++){i=c,o.push(i);for(let f=1;f<a;f++)i=i.add(c),o.push(i);c=i.double()}return o},wNAF(n,r,s){let a=e.ZERO,o=e.BASE;const c=In(n,t);for(let i=0;i<c.windows;i++){const{nextN:u,offset:f,isZero:d,isNeg:l,isNegF:p,offsetF:y}=Vs(s,i,c);s=u,d?o=o.add(Tn(p,r[y])):a=a.add(Tn(l,r[f]))}return{p:a,f:o}},wNAFUnsafe(n,r,s,a=e.ZERO){const o=In(n,t);for(let c=0;c<o.windows&&s!==Us;c++){const{nextN:i,offset:u,isZero:f,isNeg:d}=Vs(s,c,o);if(s=i,!f){const l=r[u];a=a.add(d?l.negate():l)}}return a},getPrecomputes(n,r,s){let a=An.get(r);return a||(a=this.precomputeWindow(r,n),n!==1&&An.set(r,s(a))),a},wNAFCached(n,r,s){const a=Sn(n);return this.wNAF(a,this.getPrecomputes(a,n,s),r)},wNAFCachedUnsafe(n,r,s,a){const o=Sn(n);return o===1?this.unsafeLadder(n,r,a):this.wNAFUnsafe(o,this.getPrecomputes(o,n,s),r,a)},setWindowSize(n,r){_o(r,t),Oo.set(n,r),An.delete(n)}}}function pf(e,t,n,r){if(df(n,e),ff(r,t),n.length!==r.length)throw new Error("arrays of points and scalars must have equal length");const s=e.ZERO,a=bi(BigInt(n.length)),o=a>12?a-3:a>4?a-2:a?2:1,c=Vt(o),i=new Array(Number(c)+1).fill(s),u=Math.floor((t.BITS-1)/o)*o;let f=s;for(let d=u;d>=0;d-=o){i.fill(s);for(let p=0;p<r.length;p++){const y=r[p],h=Number(y>>BigInt(d)&c);i[h]=i[h].add(n[p])}let l=s;for(let p=i.length-1,y=s;p>0;p--)y=y.add(i[p]),l=l.add(y);if(f=f.add(l),d!==0)for(let p=0;p<o;p++)f=f.double()}return f}function Lo(e){return af(e.Fp),Wt(e,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...Ro(e.n,e.nBitLength),...e,p:e.Fp.ORDER})}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */function Ws(e){e.lowS!==void 0&&it("lowS",e.lowS),e.prehash!==void 0&&it("prehash",e.prehash)}function mf(e){const t=Lo(e);Wt(t,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:n,Fp:r,a:s}=t;if(n){if(!r.eql(s,r.ZERO))throw new Error("invalid endomorphism, can only be defined for Koblitz curves that have a=0");if(typeof n!="object"||typeof n.beta!="bigint"||typeof n.splitScalar!="function")throw new Error("invalid endomorphism, expected beta: bigint and splitScalar: function")}return Object.freeze({...t})}class hf extends Error{constructor(t=""){super(t)}}const we={Err:hf,_tlv:{encode:(e,t)=>{const{Err:n}=we;if(e<0||e>256)throw new n("tlv.encode: wrong tag");if(t.length&1)throw new n("tlv.encode: unpadded data");const r=t.length/2,s=vt(r);if(s.length/2&128)throw new n("tlv.encode: long form length too big");const a=r>127?vt(s.length/2|128):"";return vt(e)+a+s+t},decode(e,t){const{Err:n}=we;let r=0;if(e<0||e>256)throw new n("tlv.encode: wrong tag");if(t.length<2||t[r++]!==e)throw new n("tlv.decode: wrong tlv");const s=t[r++],a=!!(s&128);let o=0;if(!a)o=s;else{const i=s&127;if(!i)throw new n("tlv.decode(long): indefinite length not supported");if(i>4)throw new n("tlv.decode(long): byte length is too big");const u=t.subarray(r,r+i);if(u.length!==i)throw new n("tlv.decode: length bytes not complete");if(u[0]===0)throw new n("tlv.decode(long): zero leftmost byte");for(const f of u)o=o<<8|f;if(r+=i,o<128)throw new n("tlv.decode(long): not minimal encoding")}const c=t.subarray(r,r+o);if(c.length!==o)throw new n("tlv.decode: wrong value length");return{v:c,l:t.subarray(r+o)}}},_int:{encode(e){const{Err:t}=we;if(e<xe)throw new t("integer: negative integers are not allowed");let n=vt(e);if(Number.parseInt(n[0],16)&8&&(n="00"+n),n.length&1)throw new t("unexpected DER parsing assertion: unpadded hex");return n},decode(e){const{Err:t}=we;if(e[0]&128)throw new t("invalid signature integer: negative");if(e[0]===0&&!(e[1]&128))throw new t("invalid signature integer: unnecessary leading zero");return Ne(e)}},toSig(e){const{Err:t,_int:n,_tlv:r}=we,s=ie("signature",e),{v:a,l:o}=r.decode(48,s);if(o.length)throw new t("invalid signature: left bytes after parsing");const{v:c,l:i}=r.decode(2,a),{v:u,l:f}=r.decode(2,i);if(f.length)throw new t("invalid signature: left bytes after parsing");return{r:n.decode(c),s:n.decode(u)}},hexFromSig(e){const{_tlv:t,_int:n}=we,r=t.encode(2,n.encode(e.r)),s=t.encode(2,n.encode(e.s)),a=r+s;return t.encode(48,a)}},xe=BigInt(0),K=BigInt(1);BigInt(2);const Zs=BigInt(3);BigInt(4);function yf(e){const t=mf(e),{Fp:n}=t,r=Mo(t.n,t.nBitLength),s=t.toBytes||((h,m,b)=>{const g=m.toAffine();return kn(Uint8Array.from([4]),n.toBytes(g.x),n.toBytes(g.y))}),a=t.fromBytes||(h=>{const m=h.subarray(1),b=n.fromBytes(m.subarray(0,n.BYTES)),g=n.fromBytes(m.subarray(n.BYTES,2*n.BYTES));return{x:b,y:g}});function o(h){const{a:m,b}=t,g=n.sqr(h),w=n.mul(g,h);return n.add(n.add(w,n.mul(h,m)),b)}if(!n.eql(n.sqr(t.Gy),o(t.Gx)))throw new Error("bad generator point: equation left != right");function c(h){return pa(h,K,t.n)}function i(h){const{allowedPrivateKeyLengths:m,nByteLength:b,wrapPrivateKey:g,n:w}=t;if(m&&typeof h!="bigint"){if($n(h)&&(h=Nn(h)),typeof h!="string"||!m.includes(h.length))throw new Error("invalid private key");h=h.padStart(b*2,"0")}let P;try{P=typeof h=="bigint"?h:Ne(ie("private key",h,b))}catch{throw new Error("invalid private key, expected hex or "+b+" bytes, got "+typeof h)}return g&&(P=ee(P,w)),Ve("private key",P,K,w),P}function u(h){if(!(h instanceof l))throw new Error("ProjectivePoint expected")}const f=es((h,m)=>{const{px:b,py:g,pz:w}=h;if(n.eql(w,n.ONE))return{x:b,y:g};const P=h.is0();m==null&&(m=P?n.ONE:n.inv(w));const E=n.mul(b,m),x=n.mul(g,m),v=n.mul(w,m);if(P)return{x:n.ZERO,y:n.ZERO};if(!n.eql(v,n.ONE))throw new Error("invZ was invalid");return{x:E,y:x}}),d=es(h=>{if(h.is0()){if(t.allowInfinityPoint&&!n.is0(h.py))return;throw new Error("bad point: ZERO")}const{x:m,y:b}=h.toAffine();if(!n.isValid(m)||!n.isValid(b))throw new Error("bad point: x or y not FE");const g=n.sqr(b),w=o(m);if(!n.eql(g,w))throw new Error("bad point: equation left != right");if(!h.isTorsionFree())throw new Error("bad point: not in prime-order subgroup");return!0});class l{constructor(m,b,g){if(m==null||!n.isValid(m))throw new Error("x required");if(b==null||!n.isValid(b))throw new Error("y required");if(g==null||!n.isValid(g))throw new Error("z required");this.px=m,this.py=b,this.pz=g,Object.freeze(this)}static fromAffine(m){const{x:b,y:g}=m||{};if(!m||!n.isValid(b)||!n.isValid(g))throw new Error("invalid affine point");if(m instanceof l)throw new Error("projective point not allowed");const w=P=>n.eql(P,n.ZERO);return w(b)&&w(g)?l.ZERO:new l(b,g,n.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(m){const b=n.invertBatch(m.map(g=>g.pz));return m.map((g,w)=>g.toAffine(b[w])).map(l.fromAffine)}static fromHex(m){const b=l.fromAffine(a(ie("pointHex",m)));return b.assertValidity(),b}static fromPrivateKey(m){return l.BASE.multiply(i(m))}static msm(m,b){return pf(l,r,m,b)}_setWindowSize(m){y.setWindowSize(this,m)}assertValidity(){d(this)}hasEvenY(){const{y:m}=this.toAffine();if(n.isOdd)return!n.isOdd(m);throw new Error("Field doesn't support isOdd")}equals(m){u(m);const{px:b,py:g,pz:w}=this,{px:P,py:E,pz:x}=m,v=n.eql(n.mul(b,x),n.mul(P,w)),T=n.eql(n.mul(g,x),n.mul(E,w));return v&&T}negate(){return new l(this.px,n.neg(this.py),this.pz)}double(){const{a:m,b}=t,g=n.mul(b,Zs),{px:w,py:P,pz:E}=this;let x=n.ZERO,v=n.ZERO,T=n.ZERO,I=n.mul(w,w),z=n.mul(P,P),$=n.mul(E,E),N=n.mul(w,P);return N=n.add(N,N),T=n.mul(w,E),T=n.add(T,T),x=n.mul(m,T),v=n.mul(g,$),v=n.add(x,v),x=n.sub(z,v),v=n.add(z,v),v=n.mul(x,v),x=n.mul(N,x),T=n.mul(g,T),$=n.mul(m,$),N=n.sub(I,$),N=n.mul(m,N),N=n.add(N,T),T=n.add(I,I),I=n.add(T,I),I=n.add(I,$),I=n.mul(I,N),v=n.add(v,I),$=n.mul(P,E),$=n.add($,$),I=n.mul($,N),x=n.sub(x,I),T=n.mul($,z),T=n.add(T,T),T=n.add(T,T),new l(x,v,T)}add(m){u(m);const{px:b,py:g,pz:w}=this,{px:P,py:E,pz:x}=m;let v=n.ZERO,T=n.ZERO,I=n.ZERO;const z=t.a,$=n.mul(t.b,Zs);let N=n.mul(b,P),O=n.mul(g,E),A=n.mul(w,x),C=n.add(b,g),B=n.add(P,E);C=n.mul(C,B),B=n.add(N,O),C=n.sub(C,B),B=n.add(b,w);let R=n.add(P,x);return B=n.mul(B,R),R=n.add(N,A),B=n.sub(B,R),R=n.add(g,w),v=n.add(E,x),R=n.mul(R,v),v=n.add(O,A),R=n.sub(R,v),I=n.mul(z,B),v=n.mul($,A),I=n.add(v,I),v=n.sub(O,I),I=n.add(O,I),T=n.mul(v,I),O=n.add(N,N),O=n.add(O,N),A=n.mul(z,A),B=n.mul($,B),O=n.add(O,A),A=n.sub(N,A),A=n.mul(z,A),B=n.add(B,A),N=n.mul(O,B),T=n.add(T,N),N=n.mul(R,B),v=n.mul(C,v),v=n.sub(v,N),N=n.mul(C,O),I=n.mul(R,I),I=n.add(I,N),new l(v,T,I)}subtract(m){return this.add(m.negate())}is0(){return this.equals(l.ZERO)}wNAF(m){return y.wNAFCached(this,m,l.normalizeZ)}multiplyUnsafe(m){const{endo:b,n:g}=t;Ve("scalar",m,xe,g);const w=l.ZERO;if(m===xe)return w;if(this.is0()||m===K)return this;if(!b||y.hasPrecomputes(this))return y.wNAFCachedUnsafe(this,m,l.normalizeZ);let{k1neg:P,k1:E,k2neg:x,k2:v}=b.splitScalar(m),T=w,I=w,z=this;for(;E>xe||v>xe;)E&K&&(T=T.add(z)),v&K&&(I=I.add(z)),z=z.double(),E>>=K,v>>=K;return P&&(T=T.negate()),x&&(I=I.negate()),I=new l(n.mul(I.px,b.beta),I.py,I.pz),T.add(I)}multiply(m){const{endo:b,n:g}=t;Ve("scalar",m,K,g);let w,P;if(b){const{k1neg:E,k1:x,k2neg:v,k2:T}=b.splitScalar(m);let{p:I,f:z}=this.wNAF(x),{p:$,f:N}=this.wNAF(T);I=y.constTimeNegate(E,I),$=y.constTimeNegate(v,$),$=new l(n.mul($.px,b.beta),$.py,$.pz),w=I.add($),P=z.add(N)}else{const{p:E,f:x}=this.wNAF(m);w=E,P=x}return l.normalizeZ([w,P])[0]}multiplyAndAddUnsafe(m,b,g){const w=l.BASE,P=(x,v)=>v===xe||v===K||!x.equals(w)?x.multiplyUnsafe(v):x.multiply(v),E=P(this,b).add(P(m,g));return E.is0()?void 0:E}toAffine(m){return f(this,m)}isTorsionFree(){const{h:m,isTorsionFree:b}=t;if(m===K)return!0;if(b)return b(l,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:m,clearCofactor:b}=t;return m===K?this:b?b(l,this):this.multiplyUnsafe(t.h)}toRawBytes(m=!0){return it("isCompressed",m),this.assertValidity(),s(l,this,m)}toHex(m=!0){return it("isCompressed",m),Nn(this.toRawBytes(m))}}l.BASE=new l(t.Gx,t.Gy,n.ONE),l.ZERO=new l(n.ZERO,n.ONE,n.ZERO);const p=t.nBitLength,y=lf(l,t.endo?Math.ceil(p/2):p);return{CURVE:t,ProjectivePoint:l,normPrivateKeyToScalar:i,weierstrassEquation:o,isWithinCurveOrder:c}}function bf(e){const t=Lo(e);return Wt(t,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...t})}function gf(e){const t=bf(e),{Fp:n,n:r}=t,s=n.BYTES+1,a=2*n.BYTES+1;function o(A){return ee(A,r)}function c(A){return or(A,r)}const{ProjectivePoint:i,normPrivateKeyToScalar:u,weierstrassEquation:f,isWithinCurveOrder:d}=yf({...t,toBytes(A,C,B){const R=C.toAffine(),q=n.toBytes(R.x),S=kn;return it("isCompressed",B),B?S(Uint8Array.from([C.hasEvenY()?2:3]),q):S(Uint8Array.from([4]),q,n.toBytes(R.y))},fromBytes(A){const C=A.length,B=A[0],R=A.subarray(1);if(C===s&&(B===2||B===3)){const q=Ne(R);if(!pa(q,K,n.ORDER))throw new Error("Point is not on curve");const S=f(q);let L;try{L=n.sqrt(S)}catch(re){const J=re instanceof Error?": "+re.message:"";throw new Error("Point is not on curve"+J)}const D=(L&K)===K;return(B&1)===1!==D&&(L=n.neg(L)),{x:q,y:L}}else if(C===a&&B===4){const q=n.fromBytes(R.subarray(0,n.BYTES)),S=n.fromBytes(R.subarray(n.BYTES,2*n.BYTES));return{x:q,y:S}}else{const q=s,S=a;throw new Error("invalid Point, expected length of "+q+", or uncompressed "+S+", got "+C)}}}),l=A=>Nn(Ot(A,t.nByteLength));function p(A){const C=r>>K;return A>C}function y(A){return p(A)?o(-A):A}const h=(A,C,B)=>Ne(A.slice(C,B));class m{constructor(C,B,R){Ve("r",C,K,r),Ve("s",B,K,r),this.r=C,this.s=B,R!=null&&(this.recovery=R),Object.freeze(this)}static fromCompact(C){const B=t.nByteLength;return C=ie("compactSignature",C,B*2),new m(h(C,0,B),h(C,B,2*B))}static fromDER(C){const{r:B,s:R}=we.toSig(ie("DER",C));return new m(B,R)}assertValidity(){}addRecoveryBit(C){return new m(this.r,this.s,C)}recoverPublicKey(C){const{r:B,s:R,recovery:q}=this,S=x(ie("msgHash",C));if(q==null||![0,1,2,3].includes(q))throw new Error("recovery id invalid");const L=q===2||q===3?B+t.n:B;if(L>=n.ORDER)throw new Error("recovery id 2 or 3 invalid");const D=q&1?"03":"02",Z=i.fromHex(D+l(L)),re=c(L),J=o(-S*re),be=o(R*re),de=i.BASE.multiplyAndAddUnsafe(Z,J,be);if(!de)throw new Error("point at infinify");return de.assertValidity(),de}hasHighS(){return p(this.s)}normalizeS(){return this.hasHighS()?new m(this.r,o(-this.s),this.recovery):this}toDERRawBytes(){return Qr(this.toDERHex())}toDERHex(){return we.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return Qr(this.toCompactHex())}toCompactHex(){return l(this.r)+l(this.s)}}const b={isValidPrivateKey(A){try{return u(A),!0}catch{return!1}},normPrivateKeyToScalar:u,randomPrivateKey:()=>{const A=qo(t.n);return uf(t.randomBytes(A),t.n)},precompute(A=8,C=i.BASE){return C._setWindowSize(A),C.multiply(BigInt(3)),C}};function g(A,C=!0){return i.fromPrivateKey(A).toRawBytes(C)}function w(A){const C=$n(A),B=typeof A=="string",R=(C||B)&&A.length;return C?R===s||R===a:B?R===2*s||R===2*a:A instanceof i}function P(A,C,B=!0){if(w(A))throw new Error("first arg must be private key");if(!w(C))throw new Error("second arg must be public key");return i.fromHex(C).multiply(u(A)).toRawBytes(B)}const E=t.bits2int||function(A){if(A.length>8192)throw new Error("input is too large");const C=Ne(A),B=A.length*8-t.nBitLength;return B>0?C>>BigInt(B):C},x=t.bits2int_modN||function(A){return o(E(A))},v=Vt(t.nBitLength);function T(A){return Ve("num < 2^"+t.nBitLength,A,xe,v),Ot(A,t.nByteLength)}function I(A,C,B=z){if(["recovered","canonical"].some(Ce=>Ce in B))throw new Error("sign() legacy options not supported");const{hash:R,randomBytes:q}=t;let{lowS:S,prehash:L,extraEntropy:D}=B;S==null&&(S=!0),A=ie("msgHash",A),Ws(B),L&&(A=ie("prehashed msgHash",R(A)));const Z=x(A),re=u(C),J=[T(re),T(Z)];if(D!=null&&D!==!1){const Ce=D===!0?q(n.BYTES):D;J.push(ie("extraEntropy",Ce))}const be=kn(...J),de=Z;function ln(Ce){const je=E(Ce);if(!d(je))return;const pn=c(je),nt=i.BASE.multiply(je).toAffine(),Be=o(nt.x);if(Be===xe)return;const rt=o(pn*o(de+Be*re));if(rt===xe)return;let st=(nt.x===Be?0:2)|Number(nt.y&K),De=rt;return S&&p(rt)&&(De=y(rt),st^=1),new m(Be,De,st)}return{seed:be,k2sig:ln}}const z={lowS:t.lowS,prehash:!1},$={lowS:t.lowS,prehash:!1};function N(A,C,B=z){const{seed:R,k2sig:q}=I(A,C,B),S=t;return gi(S.hash.outputLen,S.nByteLength,S.hmac)(R,q)}i.BASE._setWindowSize(8);function O(A,C,B,R=$){var st;const q=A;C=ie("msgHash",C),B=ie("publicKey",B);const{lowS:S,prehash:L,format:D}=R;if(Ws(R),"strict"in R)throw new Error("options.strict was renamed to lowS");if(D!==void 0&&D!=="compact"&&D!=="der")throw new Error("format must be compact or der");const Z=typeof q=="string"||$n(q),re=!Z&&!D&&typeof q=="object"&&q!==null&&typeof q.r=="bigint"&&typeof q.s=="bigint";if(!Z&&!re)throw new Error("invalid signature, expected Uint8Array, hex string or Signature instance");let J,be;try{if(re&&(J=new m(q.r,q.s)),Z){try{D!=="compact"&&(J=m.fromDER(q))}catch(De){if(!(De instanceof we.Err))throw De}!J&&D!=="der"&&(J=m.fromCompact(q))}be=i.fromHex(B)}catch{return!1}if(!J||S&&J.hasHighS())return!1;L&&(C=t.hash(C));const{r:de,s:ln}=J,Ce=x(C),je=c(ln),pn=o(Ce*je),nt=o(de*je),Be=(st=i.BASE.multiplyAndAddUnsafe(be,pn,nt))==null?void 0:st.toAffine();return Be?o(Be.x)===de:!1}return{CURVE:t,getPublicKey:g,getSharedSecret:P,sign:N,verify:O,ProjectivePoint:i,Signature:m,utils:b}}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */function wf(e){return{hash:e,hmac:(t,...n)=>Fo(e,t,wi(...n)),randomBytes:xi}}function xf(e,t){const n=r=>gf({...e,...wf(r)});return{...n(t),create:n}}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Go=BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),Ks=BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),vf=BigInt(1),cr=BigInt(2),Ys=(e,t)=>(e+t/cr)/t;function Ef(e){const t=Go,n=BigInt(3),r=BigInt(6),s=BigInt(11),a=BigInt(22),o=BigInt(23),c=BigInt(44),i=BigInt(88),u=e*e*e%t,f=u*u*e%t,d=se(f,n,t)*f%t,l=se(d,n,t)*f%t,p=se(l,cr,t)*u%t,y=se(p,s,t)*p%t,h=se(y,a,t)*y%t,m=se(h,c,t)*h%t,b=se(m,i,t)*m%t,g=se(b,c,t)*h%t,w=se(g,n,t)*f%t,P=se(w,o,t)*y%t,E=se(P,r,t)*u%t,x=se(E,cr,t);if(!ur.eql(ur.sqr(x),e))throw new Error("Cannot find square root");return x}const ur=Mo(Go,void 0,void 0,{sqrt:Ef}),tt=xf({a:BigInt(0),b:BigInt(7),Fp:ur,n:Ks,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),h:BigInt(1),lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:e=>{const t=Ks,n=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),r=-vf*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),s=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),a=n,o=BigInt("0x100000000000000000000000000000000"),c=Ys(a*e,t),i=Ys(-r*e,t);let u=ee(e-c*n-i*s,t),f=ee(-c*r-i*a,t);const d=u>o,l=f>o;if(d&&(u=t-u),l&&(f=t-f),u>o||f>o)throw new Error("splitScalar: Endomorphism failed, k="+e);return{k1neg:d,k1:u,k2neg:l,k2:f}}}},vi);BigInt(0);tt.ProjectivePoint;const Pf=Object.freeze(Object.defineProperty({__proto__:null,secp256k1:tt},Symbol.toStringTag,{value:"Module"}));function Tf({r:e,s:t,to:n="hex",v:r,yParity:s}){const a=(()=>{if(s===0||s===1)return s;if(r&&(r===27n||r===28n||r>=35n))return r%2n===0n?1:0;throw new Error("Invalid `v` or `yParity` value")})(),o=`0x${new tt.Signature(j(e),j(t)).toCompactHex()}${a===0?"1b":"1c"}`;return n==="hex"?o:ce(o)}async function Vr(e,t){var d,l,p;const{address:n,factory:r,factoryData:s,hash:a,signature:o,universalSignatureVerifierAddress:c=(p=(l=(d=e.chain)==null?void 0:d.contracts)==null?void 0:l.universalSignatureVerifier)==null?void 0:p.address,...i}=t,u=G(o)?o:typeof o=="object"&&"r"in o&&"s"in o?Tf(o):ne(o),f=await(async()=>!r&&!s||ho(u)?u:zu({address:r,data:s,signature:u}))();try{const y=c?{to:c,data:oe({abi:$s,functionName:"isValidSig",args:[n,a,f]}),...i}:{data:tn({abi:$s,args:[n,a,f],bytecode:mc}),...i},{data:h}=await F(e,yt,"call")(y);return Ei(h??"0x0")}catch(y){try{if(Oe(Ee(n),await Je({hash:a,signature:o})))return!0}catch{}if(y instanceof Ta)return!1;throw y}}async function If(e,{address:t,message:n,factory:r,factoryData:s,signature:a,...o}){const c=Or(n);return Vr(e,{address:t,factory:r,factoryData:s,hash:c,signature:a,...o})}async function Af(e,t){const{address:n,factory:r,factoryData:s,signature:a,message:o,primaryType:c,types:i,domain:u,...f}=t,d=ua({message:o,primaryType:c,types:i,domain:u});return Vr(e,{address:n,factory:r,factoryData:s,hash:d,signature:a,...f})}function jo(e,{emitOnBegin:t=!1,emitMissed:n=!1,onBlockNumber:r,onError:s,poll:a,pollingInterval:o=e.pollingInterval}){const c=typeof a<"u"?a:!(e.transport.type==="webSocket"||e.transport.type==="fallback"&&e.transport.transports[0].config.type==="webSocket");let i;return c?(()=>{const d=X(["watchBlockNumber",e.uid,t,n,o]);return Pe(d,{onBlockNumber:r,onError:s},l=>et(async()=>{var p;try{const y=await F(e,bt,"getBlockNumber")({cacheTime:0});if(i){if(y===i)return;if(y-i>1&&n)for(let h=i+1n;h<y;h++)l.onBlockNumber(h,i),i=h}(!i||y>i)&&(l.onBlockNumber(y,i),i=y)}catch(y){(p=l.onError)==null||p.call(l,y)}},{emitOnBegin:t,interval:o}))})():(()=>{const d=X(["watchBlockNumber",e.uid,t,n]);return Pe(d,{onBlockNumber:r,onError:s},l=>{let p=!0,y=()=>p=!1;return(async()=>{try{const h=(()=>{if(e.transport.type==="fallback"){const b=e.transport.transports.find(g=>g.config.type==="webSocket");return b?b.value:e.transport}return e.transport})(),{unsubscribe:m}=await h.subscribe({params:["newHeads"],onData(b){var w;if(!p)return;const g=j((w=b.result)==null?void 0:w.number);l.onBlockNumber(g,i),i=g},onError(b){var g;(g=l.onError)==null||g.call(l,b)}});y=m,p||y()}catch(h){s==null||s(h)}})(),()=>y()})})()}async function Sf(e,{confirmations:t=1,hash:n,onReplaced:r,pollingInterval:s=e.pollingInterval,retryCount:a=6,retryDelay:o=({count:i})=>~~(1<<i)*200,timeout:c=18e4}){const i=X(["waitForTransactionReceipt",e.uid,n]);let u,f,d,l=!1;const{promise:p,resolve:y,reject:h}=kr(),m=c?setTimeout(()=>h(new Vi({hash:n})),c):void 0,b=Pe(i,{onReplaced:r,resolve:y,reject:h},g=>{const w=F(e,jo,"watchBlockNumber")({emitMissed:!0,emitOnBegin:!0,poll:!0,pollingInterval:s,async onBlockNumber(P){const E=v=>{clearTimeout(m),w(),v(),b()};let x=P;if(!l)try{if(d){if(t>1&&(!d.blockNumber||x-d.blockNumber+1n<t))return;E(()=>g.resolve(d));return}if(u||(l=!0,await Yn(async()=>{u=await F(e,Lr,"getTransaction")({hash:n}),u.blockNumber&&(x=u.blockNumber)},{delay:o,retryCount:a}),l=!1),d=await F(e,Xn,"getTransactionReceipt")({hash:n}),t>1&&(!d.blockNumber||x-d.blockNumber+1n<t))return;E(()=>g.resolve(d))}catch(v){if(v instanceof Ea||v instanceof Pa){if(!u){l=!1;return}try{f=u,l=!0;const T=await Yn(()=>F(e,le,"getBlock")({blockNumber:x,includeTransactions:!0}),{delay:o,retryCount:a,shouldRetry:({error:$})=>$ instanceof Aa});l=!1;const I=T.transactions.find(({from:$,nonce:N})=>$===f.from&&N===f.nonce);if(!I||(d=await F(e,Xn,"getTransactionReceipt")({hash:I.hash}),t>1&&(!d.blockNumber||x-d.blockNumber+1n<t)))return;let z="replaced";I.to===f.to&&I.value===f.value&&I.input===f.input?z="repriced":I.from===I.to&&I.value===0n&&(z="cancelled"),E(()=>{var $;($=g.onReplaced)==null||$.call(g,{reason:z,replacedTransaction:f,transaction:I,transactionReceipt:d}),g.resolve(d)})}catch(T){E(()=>g.reject(T))}}else E(()=>g.reject(v))}}})});return p}function Cf(e,{blockTag:t="latest",emitMissed:n=!1,emitOnBegin:r=!1,onBlock:s,onError:a,includeTransactions:o,poll:c,pollingInterval:i=e.pollingInterval}){const u=typeof c<"u"?c:!(e.transport.type==="webSocket"||e.transport.type==="fallback"&&e.transport.transports[0].config.type==="webSocket"),f=o??!1;let d;return u?(()=>{const y=X(["watchBlocks",e.uid,t,n,r,f,i]);return Pe(y,{onBlock:s,onError:a},h=>et(async()=>{var m;try{const b=await F(e,le,"getBlock")({blockTag:t,includeTransactions:f});if(b.number&&(d!=null&&d.number)){if(b.number===d.number)return;if(b.number-d.number>1&&n)for(let g=(d==null?void 0:d.number)+1n;g<b.number;g++){const w=await F(e,le,"getBlock")({blockNumber:g,includeTransactions:f});h.onBlock(w,d),d=w}}(!(d!=null&&d.number)||t==="pending"&&!(b!=null&&b.number)||b.number&&b.number>d.number)&&(h.onBlock(b,d),d=b)}catch(b){(m=h.onError)==null||m.call(h,b)}},{emitOnBegin:r,interval:i}))})():(()=>{let y=!0,h=!0,m=()=>y=!1;return(async()=>{try{r&&F(e,le,"getBlock")({blockTag:t,includeTransactions:f}).then(w=>{y&&h&&(s(w,void 0),h=!1)});const b=(()=>{if(e.transport.type==="fallback"){const w=e.transport.transports.find(P=>P.config.type==="webSocket");return w?w.value:e.transport}return e.transport})(),{unsubscribe:g}=await b.subscribe({params:["newHeads"],async onData(w){if(!y)return;const P=await F(e,le,"getBlock")({blockNumber:w.blockNumber,includeTransactions:f}).catch(()=>{});y&&(s(P,d),h=!1,d=P)},onError(w){a==null||a(w)}});m=g,y||m()}catch(b){a==null||a(b)}})(),()=>m()})()}function Bf(e,{address:t,args:n,batch:r=!0,event:s,events:a,fromBlock:o,onError:c,onLogs:i,poll:u,pollingInterval:f=e.pollingInterval,strict:d}){const l=typeof u<"u"?u:typeof o=="bigint"?!0:!(e.transport.type==="webSocket"||e.transport.type==="fallback"&&e.transport.transports[0].config.type==="webSocket"),p=d??!1;return l?(()=>{const m=X(["watchEvent",t,n,r,e.uid,s,f,o]);return Pe(m,{onLogs:i,onError:c},b=>{let g;o!==void 0&&(g=o-1n);let w,P=!1;const E=et(async()=>{var x;if(!P){try{w=await F(e,no,"createEventFilter")({address:t,args:n,event:s,events:a,strict:p,fromBlock:o})}catch{}P=!0;return}try{let v;if(w)v=await F(e,nn,"getFilterChanges")({filter:w});else{const T=await F(e,bt,"getBlockNumber")({});g&&g!==T?v=await F(e,Cr,"getLogs")({address:t,args:n,event:s,events:a,fromBlock:g+1n,toBlock:T}):v=[],g=T}if(v.length===0)return;if(r)b.onLogs(v);else for(const T of v)b.onLogs([T])}catch(v){w&&v instanceof Gt&&(P=!1),(x=b.onError)==null||x.call(b,v)}},{emitOnBegin:!0,interval:f});return async()=>{w&&await F(e,rn,"uninstallFilter")({filter:w}),E()}})})():(()=>{let m=!0,b=()=>m=!1;return(async()=>{try{const g=(()=>{if(e.transport.type==="fallback"){const x=e.transport.transports.find(v=>v.config.type==="webSocket");return x?x.value:e.transport}return e.transport})(),w=a??(s?[s]:void 0);let P=[];w&&(P=[w.flatMap(v=>ut({abi:[v],eventName:v.name,args:n}))],s&&(P=P[0]));const{unsubscribe:E}=await g.subscribe({params:["logs",{address:t,topics:P}],onData(x){var T;if(!m)return;const v=x.result;try{const{eventName:I,args:z}=oa({abi:w??[],data:v.data,topics:v.topics,strict:p}),$=Ie(v,{args:z,eventName:I});i([$])}catch(I){let z,$;if(I instanceof ia||I instanceof ca){if(d)return;z=I.abiItem.name,$=(T=I.abiItem.inputs)==null?void 0:T.some(O=>!("name"in O&&O.name))}const N=Ie(v,{args:$?[]:{},eventName:z});i([N])}},onError(x){c==null||c(x)}});b=E,m||b()}catch(g){c==null||c(g)}})(),()=>b()})()}function kf(e,{batch:t=!0,onError:n,onTransactions:r,poll:s,pollingInterval:a=e.pollingInterval}){return(typeof s<"u"?s:e.transport.type!=="webSocket")?(()=>{const u=X(["watchPendingTransactions",e.uid,t,a]);return Pe(u,{onTransactions:r,onError:n},f=>{let d;const l=et(async()=>{var p;try{if(!d)try{d=await F(e,ro,"createPendingTransactionFilter")({});return}catch(h){throw l(),h}const y=await F(e,nn,"getFilterChanges")({filter:d});if(y.length===0)return;if(t)f.onTransactions(y);else for(const h of y)f.onTransactions([h])}catch(y){(p=f.onError)==null||p.call(f,y)}},{emitOnBegin:!0,interval:a});return async()=>{d&&await F(e,rn,"uninstallFilter")({filter:d}),l()}})})():(()=>{let u=!0,f=()=>u=!1;return(async()=>{try{const{unsubscribe:d}=await e.transport.subscribe({params:["newPendingTransactions"],onData(l){if(!u)return;const p=l.result;r([p])},onError(l){n==null||n(l)}});f=d,u||f()}catch(d){n==null||n(d)}})(),()=>f()})()}function $f(e){var d,l,p;const{scheme:t,statement:n,...r}=((d=e.match(Nf))==null?void 0:d.groups)??{},{chainId:s,expirationTime:a,issuedAt:o,notBefore:c,requestId:i,...u}=((l=e.match(Ff))==null?void 0:l.groups)??{},f=(p=e.split("Resources:")[1])==null?void 0:p.split(`
- `).slice(1);return{...r,...u,...s?{chainId:Number(s)}:{},...a?{expirationTime:new Date(a)}:{},...o?{issuedAt:new Date(o)}:{},...c?{notBefore:new Date(c)}:{},...i?{requestId:i}:{},...f?{resources:f}:{},...t?{scheme:t}:{},...n?{statement:n}:{}}}const Nf=/^(?:(?<scheme>[a-zA-Z][a-zA-Z0-9+-.]*):\/\/)?(?<domain>[a-zA-Z0-9+-.]*(?::[0-9]{1,5})?) (?:wants you to sign in with your Ethereum account:\n)(?<address>0x[a-fA-F0-9]{40})\n\n(?:(?<statement>.*)\n\n)?/,Ff=/(?:URI: (?<uri>.+))\n(?:Version: (?<version>.+))\n(?:Chain ID: (?<chainId>\d+))\n(?:Nonce: (?<nonce>[a-zA-Z0-9]+))\n(?:Issued At: (?<issuedAt>.+))(?:\nExpiration Time: (?<expirationTime>.+))?(?:\nNot Before: (?<notBefore>.+))?(?:\nRequest ID: (?<requestId>.+))?/;function Rf(e){const{address:t,domain:n,message:r,nonce:s,scheme:a,time:o=new Date}=e;if(n&&r.domain!==n||s&&r.nonce!==s||a&&r.scheme!==a||r.expirationTime&&o>=r.expirationTime||r.notBefore&&o<r.notBefore)return!1;try{if(!r.address||t&&!Oe(r.address,t))return!1}catch{return!1}return!0}async function Mf(e,t){const{address:n,domain:r,message:s,nonce:a,scheme:o,signature:c,time:i=new Date,...u}=t,f=$f(s);if(!f.address||!Rf({address:n,domain:r,message:f,nonce:a,scheme:o,time:i}))return!1;const l=Or(s);return Vr(e,{address:f.address,hash:l,signature:c,...u})}function zf(e){return{call:t=>yt(e,t),createAccessList:t=>to(e,t),createBlockFilter:()=>su(e),createContractEventFilter:t=>gr(e,t),createEventFilter:t=>no(e,t),createPendingTransactionFilter:()=>ro(e),estimateContractGas:t=>qa(e,t),estimateGas:t=>Sr(e,t),getBalance:t=>za(e,t),getBlobBaseFee:()=>au(e),getBlock:t=>le(e,t),getBlockNumber:t=>bt(e,t),getBlockTransactionCount:t=>ou(e,t),getBytecode:t=>Ms(e,t),getChainId:()=>Xe(e),getCode:t=>Ms(e,t),getContractEvents:t=>Br(e,t),getEip712Domain:t=>cu(e,t),getEnsAddress:t=>jc(e,t),getEnsAvatar:t=>tu(e,t),getEnsName:t=>nu(e,t),getEnsResolver:t=>ru(e,t),getEnsText:t=>eo(e,t),getFeeHistory:t=>fu(e,t),estimateFeesPerGas:t=>oc(e,t),getFilterChanges:t=>nn(e,t),getFilterLogs:t=>lu(e,t),getGasPrice:()=>Er(e),getLogs:t=>Cr(e,t),getProof:t=>Yu(e,t),estimateMaxPriorityFeePerGas:t=>ac(e,t),getStorageAt:t=>Ju(e,t),getTransaction:t=>Lr(e,t),getTransactionConfirmations:t=>Xu(e,t),getTransactionCount:t=>Xt(e,t),getTransactionReceipt:t=>Xn(e,t),multicall:t=>Qu(e,t),prepareTransactionRequest:t=>Qt(e,t),readContract:t=>he(e,t),sendRawTransaction:t=>Nr(e,t),simulate:t=>Qn(e,t),simulateBlocks:t=>Qn(e,t),simulateCalls:t=>Qd(e,t),simulateContract:t=>Da(e,t),verifyMessage:t=>If(e,t),verifySiweMessage:t=>Mf(e,t),verifyTypedData:t=>Af(e,t),uninstallFilter:t=>rn(e,t),waitForTransactionReceipt:t=>Sf(e,t),watchBlocks:t=>Cf(e,t),watchBlockNumber:t=>jo(e,t),watchContractEvent:t=>Ha(e,t),watchEvent:t=>Bf(e,t),watchPendingTransactions:t=>kf(e,t)}}function i0(e){const{key:t="public",name:n="Public Client"}=e;return Rr({...e,key:t,name:n,type:"publicClient"}).extend(zf)}async function qf(e,{hash:t}){await e.request({method:`${e.mode}_dropTransaction`,params:[t]})}async function _f(e){return e.request({method:`${e.mode}_dumpState`})}async function Of(e){return e.mode==="ganache"?await e.request({method:"eth_mining"}):await e.request({method:`${e.mode}_getAutomine`})}async function Lf(e){return await e.request({method:"txpool_content"})}async function Gf(e){const{pending:t,queued:n}=await e.request({method:"txpool_status"});return{pending:V(t),queued:V(n)}}async function jf(e,{address:t}){await e.request({method:`${e.mode}_impersonateAccount`,params:[t]})}async function Df(e,{seconds:t}){return await e.request({method:"evm_increaseTime",params:[M(t)]})}async function Hf(e){return await e.request({method:"txpool_inspect"})}async function Uf(e,{state:t}){await e.request({method:`${e.mode}_loadState`,params:[t]})}async function Vf(e,{blocks:t,interval:n}){e.mode==="ganache"?await e.request({method:"evm_mine",params:[{blocks:M(t)}]}):await e.request({method:`${e.mode}_mine`,params:[M(t),M(n||0)]})}async function Wf(e){await e.request({method:`${e.mode}_removeBlockTimestampInterval`})}async function Zf(e,{blockNumber:t,jsonRpcUrl:n}={}){await e.request({method:`${e.mode}_reset`,params:[{forking:{blockNumber:Number(t),jsonRpcUrl:n}}]})}async function Kf(e,{id:t}){await e.request({method:"evm_revert",params:[t]})}async function Yf(e,t){var b,g,w;const{accessList:n,data:r,from:s,gas:a,gasPrice:o,maxFeePerGas:c,maxPriorityFeePerGas:i,nonce:u,to:f,value:d,...l}=t,p=(w=(g=(b=e.chain)==null?void 0:b.formatters)==null?void 0:g.transactionRequest)==null?void 0:w.format,h=(p||_e)({...ht(l,{format:p}),accessList:n,data:r,from:s,gas:a,gasPrice:o,maxFeePerGas:c,maxPriorityFeePerGas:i,nonce:u,to:f,value:d});return await e.request({method:"eth_sendUnsignedTransaction",params:[h]})}async function Jf(e,t){e.mode==="ganache"?t?await e.request({method:"miner_start"}):await e.request({method:"miner_stop"}):await e.request({method:"evm_setAutomine",params:[t]})}async function Xf(e,{address:t,value:n}){e.mode==="ganache"?await e.request({method:"evm_setAccountBalance",params:[t,M(n)]}):await e.request({method:`${e.mode}_setBalance`,params:[t,M(n)]})}async function Qf(e,{gasLimit:t}){await e.request({method:"evm_setBlockGasLimit",params:[M(t)]})}async function el(e,{interval:t}){const n=e.mode==="hardhat"?t*1e3:t;await e.request({method:`${e.mode}_setBlockTimestampInterval`,params:[n]})}async function tl(e,{address:t,bytecode:n}){e.mode==="ganache"?await e.request({method:"evm_setAccountCode",params:[t,n]}):await e.request({method:`${e.mode}_setCode`,params:[t,n]})}async function nl(e,{address:t}){await e.request({method:`${e.mode}_setCoinbase`,params:[t]})}async function rl(e,{interval:t}){const n=e.mode==="hardhat"?t*1e3:t;await e.request({method:"evm_setIntervalMining",params:[n]})}async function sl(e,t){await e.request({method:`${e.mode}_setLoggingEnabled`,params:[t]})}async function al(e,{gasPrice:t}){await e.request({method:`${e.mode}_setMinGasPrice`,params:[M(t)]})}async function ol(e,{baseFeePerGas:t}){await e.request({method:`${e.mode}_setNextBlockBaseFeePerGas`,params:[M(t)]})}async function il(e,{timestamp:t}){await e.request({method:"evm_setNextBlockTimestamp",params:[M(t)]})}async function cl(e,{address:t,nonce:n}){await e.request({method:`${e.mode}_setNonce`,params:[t,M(n)]})}async function ul(e,t){await e.request({method:`${e.mode}_setRpcUrl`,params:[t]})}async function dl(e,{address:t,index:n,value:r}){await e.request({method:`${e.mode}_setStorageAt`,params:[t,typeof n=="number"?M(n):n,r]})}async function fl(e){return await e.request({method:"evm_snapshot"})}async function ll(e,{address:t}){await e.request({method:`${e.mode}_stopImpersonatingAccount`,params:[t]})}function pl({mode:e}){return t=>{const n=t.extend(()=>({mode:e}));return{dropTransaction:r=>qf(n,r),dumpState:()=>_f(n),getAutomine:()=>Of(n),getTxpoolContent:()=>Lf(n),getTxpoolStatus:()=>Gf(n),impersonateAccount:r=>jf(n,r),increaseTime:r=>Df(n,r),inspectTxpool:()=>Hf(n),loadState:r=>Uf(n,r),mine:r=>Vf(n,r),removeBlockTimestampInterval:()=>Wf(n),reset:r=>Zf(n,r),revert:r=>Kf(n,r),sendUnsignedTransaction:r=>Yf(n,r),setAutomine:r=>Jf(n,r),setBalance:r=>Xf(n,r),setBlockGasLimit:r=>Qf(n,r),setBlockTimestampInterval:r=>el(n,r),setCode:r=>tl(n,r),setCoinbase:r=>nl(n,r),setIntervalMining:r=>rl(n,r),setLoggingEnabled:r=>sl(n,r),setMinGasPrice:r=>al(n,r),setNextBlockBaseFeePerGas:r=>ol(n,r),setNextBlockTimestamp:r=>il(n,r),setNonce:r=>cl(n,r),setRpcUrl:r=>ul(n,r),setStorageAt:r=>dl(n,r),snapshot:()=>fl(n),stopImpersonatingAccount:r=>ll(n,r)}}}function c0(e){const{key:t="test",name:n="Test Client",mode:r}=e;return Rr({...e,key:t,name:n,type:"testClient"}).extend(a=>({mode:r,...pl({mode:r})(a)}))}async function ml(e,{chain:t}){const{id:n,name:r,nativeCurrency:s,rpcUrls:a,blockExplorers:o}=t;await e.request({method:"wallet_addEthereumChain",params:[{chainId:M(n),chainName:r,nativeCurrency:s,rpcUrls:a.default.http,blockExplorerUrls:o?Object.values(o).map(({url:c})=>c):void 0}]},{dedupe:!0,retryCount:0})}function hl(e,t){const{abi:n,args:r,bytecode:s,...a}=t,o=tn({abi:n,args:r,bytecode:s});return Fr(e,{...a,...a.authorizationList?{to:null}:{},data:o})}async function yl(e){var n;return((n=e.account)==null?void 0:n.type)==="local"?[e.account.address]:(await e.request({method:"eth_accounts"},{dedupe:!0})).map(r=>ta(r))}async function bl(e,t={}){const{account:n=e.account,chainId:r}=t,s=n?W(n):void 0,a=await e.request({method:"wallet_getCapabilities",params:[s==null?void 0:s.address]}),o={};for(const[c,i]of Object.entries(a))o[Number(c)]=i;return typeof r=="number"?o[r]:o}async function gl(e){return await e.request({method:"wallet_getPermissions"},{dedupe:!0})}async function Do(e,t){var i;const{account:n=e.account,chainId:r,nonce:s}=t;if(!n)throw new Se({docsPath:"/docs/eip7702/prepareAuthorization"});const a=W(n),o=(()=>{if(t.executor)return t.executor==="self"?t.executor:W(t.executor)})(),c={address:t.contractAddress??t.address,chainId:r,nonce:s};return typeof c.chainId>"u"&&(c.chainId=((i=e.chain)==null?void 0:i.id)??await F(e,Xe,"getChainId")({})),typeof c.nonce>"u"&&(c.nonce=await F(e,Xt,"getTransactionCount")({address:a.address,blockTag:"pending"}),(o==="self"||o!=null&&o.address&&Oe(o.address,a.address))&&(c.nonce+=1)),c}async function wl(e){return(await e.request({method:"eth_requestAccounts"},{dedupe:!0,retryCount:0})).map(n=>Ee(n))}async function xl(e,t){return e.request({method:"wallet_requestPermissions",params:[t]},{retryCount:0})}async function vl(e,t){const{account:n=e.account,capabilities:r,chain:s=e.chain,forceAtomic:a=!1,id:o,version:c="2.0.0"}=t;if(typeof n>"u")throw new Se({docsPath:"/docs/actions/wallet/sendCalls"});const i=n?W(n):null,u=t.calls.map(f=>{const d=f;return{data:d.abi?oe({abi:d.abi,functionName:d.functionName,args:d.args}):d.data,to:d.to,value:d.value?M(d.value):void 0}});try{const f=await e.request({method:"wallet_sendCalls",params:[{atomicRequired:a,calls:u,capabilities:r,chainId:M(s.id),from:i==null?void 0:i.address,id:o,version:c}]},{retryCount:0});return typeof f=="string"?{id:f}:f}catch(f){throw Va(f,{...t,account:i,chain:t.chain})}}async function El(e,t){const{id:n}=t;await e.request({method:"wallet_showCallsStatus",params:[n]})}async function Pl(e,t){const{account:n=e.account}=t;if(!n)throw new Se({docsPath:"/docs/eip7702/signAuthorization"});const r=W(n);if(!r.signAuthorization)throw new $t({docsPath:"/docs/eip7702/signAuthorization",metaMessages:["The `signAuthorization` Action does not support JSON-RPC Accounts."],type:r.type});const s=await Do(e,t);return r.signAuthorization(s)}async function Tl(e,{account:t=e.account,message:n}){if(!t)throw new Se({docsPath:"/docs/actions/wallet/signMessage"});const r=W(t);if(r.signMessage)return r.signMessage({message:n});const s=typeof n=="string"?ot(n):n.raw instanceof Uint8Array?_(n.raw):n.raw;return e.request({method:"personal_sign",params:[s,r.address]},{retryCount:0})}async function Il(e,t){var u,f,d,l;const{account:n=e.account,chain:r=e.chain,...s}=t;if(!n)throw new Se({docsPath:"/docs/actions/wallet/signTransaction"});const a=W(n);Le({account:a,...t});const o=await F(e,Xe,"getChainId")({});r!==null&&Ua({currentChainId:o,chain:r});const c=(r==null?void 0:r.formatters)||((u=e.chain)==null?void 0:u.formatters),i=((f=c==null?void 0:c.transactionRequest)==null?void 0:f.format)||_e;return a.signTransaction?a.signTransaction({...s,chainId:o},{serializer:(l=(d=e.chain)==null?void 0:d.serializers)==null?void 0:l.transaction}):await e.request({method:"eth_signTransaction",params:[{...i(s),chainId:M(o),from:a.address}]},{retryCount:0})}async function Al(e,t){const{account:n=e.account,domain:r,message:s,primaryType:a}=t;if(!n)throw new Se({docsPath:"/docs/actions/wallet/signTypedData"});const o=W(n),c={EIP712Domain:Pi({domain:r}),...t.types};if(Ti({domain:r,message:s,primaryType:a,types:c}),o.signTypedData)return o.signTypedData({domain:r,message:s,primaryType:a,types:c});const i=Ii({domain:r,message:s,primaryType:a,types:c});return e.request({method:"eth_signTypedData_v4",params:[o.address,i]},{retryCount:0})}async function Sl(e,{id:t}){await e.request({method:"wallet_switchEthereumChain",params:[{chainId:M(t)}]},{retryCount:0})}async function Cl(e,t){return await e.request({method:"wallet_watchAsset",params:t},{retryCount:0})}function Bl(e){return{addChain:t=>ml(e,t),deployContract:t=>hl(e,t),getAddresses:()=>yl(e),getCallsStatus:t=>Za(e,t),getCapabilities:()=>bl(e),getChainId:()=>Xe(e),getPermissions:()=>gl(e),prepareAuthorization:t=>Do(e,t),prepareTransactionRequest:t=>Qt(e,t),requestAddresses:()=>wl(e),requestPermissions:t=>xl(e,t),sendCalls:t=>vl(e,t),sendRawTransaction:t=>Nr(e,t),sendTransaction:t=>Fr(e,t),showCallsStatus:t=>El(e,t),signAuthorization:t=>Pl(e,t),signMessage:t=>Tl(e,t),signTransaction:t=>Il(e,t),signTypedData:t=>Al(e,t),switchChain:t=>Sl(e,t),waitForCallsStatus:t=>Cc(e,t),watchAsset:t=>Cl(e,t),writeContract:t=>Wa(e,t)}}function u0(e){const{key:t="wallet",name:n="Wallet Client",transport:r}=e;return Rr({...e,key:t,name:n,transport:r,type:"walletClient"}).extend(Bl)}function d0(e,t={}){const{keepAlive:n,key:r="webSocket",methods:s,name:a="WebSocket JSON-RPC",reconnect:o,retryDelay:c}=t;return({chain:i,retryCount:u,timeout:f})=>{var h;const d=t.retryCount??u,l=f??t.timeout??1e4,p=e||((h=i==null?void 0:i.rpcUrls.default.webSocket)==null?void 0:h[0]),y={keepAlive:n,reconnect:o};if(!p)throw new Ya;return sn({key:r,methods:s,name:a,async request({method:m,params:b}){const g={method:m,params:b},w=await Ft(p,y),{error:P,result:E}=await w.requestAsync({body:g,timeout:l});if(P)throw new hr({body:g,error:P,url:p});return E},retryCount:d,retryDelay:c,timeout:l,type:"webSocket"},{getSocket(){return xu(p)},getRpcClient(){return Ft(p,y)},async subscribe({params:m,onData:b,onError:g}){const w=await Ft(p,y),{result:P}=await new Promise((E,x)=>w.request({body:{method:"eth_subscribe",params:m},onError(v){x(v),g==null||g(v)},onResponse(v){if(v.error){x(v.error),g==null||g(v.error);return}if(typeof v.id=="number"){E(v);return}v.method==="eth_subscription"&&b(v.params)}}));return{subscriptionId:P,async unsubscribe(){return new Promise(E=>w.request({body:{method:"eth_unsubscribe",params:[P]},onResponse:E}))}}}})}}class f0 extends Error{constructor(t,n){super(n),Object.defineProperty(this,"code",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"details",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.code=t,this.details=n}}const Cn="/docs/contract/decodeDeployData";function l0(e){const{abi:t,bytecode:n,data:r}=e;if(r===n)return{bytecode:n};const s=t.find(o=>"type"in o&&o.type==="constructor");if(!s)throw new sa({docsPath:Cn});if(!("inputs"in s))throw new qt({docsPath:Cn});if(!s.inputs||s.inputs.length===0)throw new qt({docsPath:Cn});return{args:Ht(s.inputs,`0x${r.replace(n,"")}`),bytecode:n}}function p0({r:e,yParityAndS:t}){const n=ce(t),r=n[0]&128?1:0,s=n;return r===1&&(s[0]&=127),{r:e,s:ne(s),yParity:r}}function m0(e){const{r:t,s:n}=tt.Signature.fromCompact(e.slice(2,130));return{r:M(t,{size:32}),yParityAndS:M(n,{size:32})}}function h0(e){const{r:t,s:n}=tt.Signature.fromCompact(e.slice(2,130)),r=+`0x${e.slice(130)}`,[s,a]=(()=>{if(r===0||r===1)return[void 0,r];if(r===27)return[BigInt(r),0];if(r===28)return[BigInt(r),1];throw new Error("Invalid yParityOrV value")})();return typeof s<"u"?{r:M(t,{size:32}),s:M(n,{size:32}),v:s,yParity:a}:{r:M(t,{size:32}),s:M(n,{size:32}),yParity:a}}async function y0(e){const{serializedTransaction:t,signature:n}=e,r=_u(t),s=n??{r:r.r,s:r.s,v:r.v,yParity:r.yParity},a=vu({...r,r:void 0,s:void 0,v:void 0,yParity:void 0,sidecars:void 0});return await Je({hash:ve(a),signature:s})}function b0(e){const{r:t,s:n,v:r,yParity:s}=e,a=Number(s??r-27n);let o=n;if(a===1){const c=ce(n);c[0]|=128,o=ne(c)}return{r:t,yParityAndS:o}}function g0({r:e,yParityAndS:t}){return`0x${new tt.Signature(j(e),j(t)).toCompactHex()}`}function w0(e){const{sidecars:t,version:n}=e,r=e.to??(typeof t[0].blob=="string"?"hex":"bytes"),s=[];for(const{commitment:a}of t)s.push(Ca({commitment:a,to:r,version:n}));return s}function x0(e){const t=e.to??(typeof e.blobs[0]=="string"?"hex":"bytes"),n=typeof e.blobs[0]=="string"?e.blobs.map(c=>ce(c)):e.blobs,r=n.reduce((c,i)=>c+i.length,0),s=zt(new Uint8Array(r));let a=!0;for(const c of n){const i=zt(c);for(;a&&i.position<c.length;){i.incrementPosition(1);let u=31;c.length-i.position<31&&(u=c.length-i.position);for(const f in Array.from({length:u})){const d=i.readByte();if(d===128&&!i.inspectBytes(i.remaining).includes(128)){a=!1;break}s.pushByte(d)}}}const o=s.bytes.slice(0,s.position);return t==="hex"?ne(o):o}function kl({blobToKzgCommitment:e,computeBlobKzgProof:t}){return{blobToKzgCommitment:e,computeBlobKzgProof:t}}function v0(e,t){try{e.loadTrustedSetup(t)}catch(n){const r=n;if(!r.message.includes("trusted setup is already loaded"))throw r}return kl(e)}export{sa as AbiConstructorNotFoundError,qt as AbiConstructorParamsNotFoundError,T0 as AbiDecodingDataSizeInvalidError,I0 as AbiDecodingDataSizeTooSmallError,fr as AbiDecodingZeroDataError,A0 as AbiEncodingArrayLengthMismatchError,S0 as AbiEncodingBytesSizeMismatchError,ni as AbiEncodingLengthMismatchError,Jo as AbiErrorInputsNotFoundError,Kr as AbiErrorNotFoundError,Zo as AbiErrorSignatureNotFoundError,C0 as AbiEventNotFoundError,B0 as AbiEventSignatureEmptyTopicsError,k0 as AbiEventSignatureNotFoundError,We as AbiFunctionNotFoundError,ra as AbiFunctionOutputsNotFoundError,Yo as AbiFunctionSignatureNotFoundError,_i as AccountStateConflictError,ns as AtomicReadyWalletRejectedUpgradeError,ts as AtomicityNotSupportedError,k as BaseError,rc as BaseFeeScalarError,Aa as BlockNotFoundError,rs as BundleTooLargeError,oi as BytesSizeMismatchError,Ta as CallExecutionError,us as ChainDisconnectedError,Zn as ChainDoesNotSupportContract,hc as ChainMismatchError,yc as ChainNotFoundError,$0 as CircularReferenceError,Ga as ClientChainNotConfiguredError,Wi as ContractFunctionExecutionError,zn as ContractFunctionRevertedError,Zi as ContractFunctionZeroDataError,Ki as CounterfactualDeploymentFailedError,ia as DecodeLogDataMismatch,ca as DecodeLogTopicsMismatch,as as DuplicateIdError,f0 as EIP1193ProviderRpcError,vr as Eip1559FeesNotSupportedError,at as EnsAvatarInvalidNftUriError,Hc as EnsAvatarUnsupportedNamespaceError,qr as EnsAvatarUriResolutionError,ec as EstimateGasExecutionError,$e as ExecutionRevertedError,Me as FeeCapTooHighError,qn as FeeCapTooLowError,Gi as FeeConflictError,N0 as FilterTypeNotSupportedError,Fe as HttpRequestError,Gn as InsufficientFundsError,F0 as IntegerOutOfRangeError,Lt as InternalRpcError,jn as IntrinsicGasTooHighError,Dn as IntrinsicGasTooLowError,R0 as InvalidAbiDecodingTypeError,M0 as InvalidAbiEncodingTypeError,z0 as InvalidAbiItemError,Ho as InvalidAbiParameterError,Uo as InvalidAbiParametersError,q0 as InvalidAbiTypeParameterError,me as InvalidAddressError,Xo as InvalidArrayError,_0 as InvalidBytesBooleanError,en as InvalidChainIdError,Uu as InvalidDecimalNumberError,O0 as InvalidDefinitionTypeError,L0 as InvalidDomainError,G0 as InvalidFunctionModifierError,j0 as InvalidHexBooleanError,ui as InvalidHexValueError,Gt as InvalidInputRpcError,va as InvalidLegacyVError,D0 as InvalidModifierError,H0 as InvalidParameterError,ys as InvalidParamsRpcError,U0 as InvalidParenthesisError,V0 as InvalidPrimaryTypeError,gs as InvalidRequestRpcError,ji as InvalidSerializableTransactionError,pt as InvalidSerializedTransactionError,Di as InvalidSerializedTransactionTypeError,W0 as InvalidSignatureError,Hi as InvalidStorageKeySizeError,Z0 as InvalidStructSignatureError,K0 as InvalidStructTypeError,ps as JsonRpcVersionUnsupportedError,Fn as LimitExceededRpcError,sc as MaxFeePerGasTooLowError,bs as MethodNotFoundRpcError,Et as MethodNotSupportedRpcError,Ln as NonceMaxValueError,_n as NonceTooHighError,On as NonceTooLowError,ws as ParseRpcError,ds as ProviderDisconnectedError,Rp as ProviderRpcError,Yt as RawContractError,hs as ResourceNotFoundRpcError,ms as ResourceUnavailableRpcError,Mp as RpcError,hr as RpcRequestError,Y0 as SizeExceedsPaddingSizeError,J0 as SizeOverflowError,X0 as SliceOffsetOutOfBoundsError,jt as SocketClosedError,Q0 as SolidityProtectedKeywordError,Oi as StateAssignmentConflictError,cs as SwitchChainError,Mn as TimeoutError,ct as TipAboveFeeCapError,Ui as TransactionExecutionError,Ea as TransactionNotFoundError,Pa as TransactionReceiptNotFoundError,Rn as TransactionRejectedRpcError,Hn as TransactionTypeNotSupportedError,ls as UnauthorizedProviderError,ss as UnknownBundleIdError,mt as UnknownNodeError,Si as UnknownRpcError,ep as UnknownSignatureError,tp as UnknownTypeError,os as UnsupportedChainIdError,is as UnsupportedNonOptionalCapabilityError,ci as UnsupportedPackedAbiType,fs as UnsupportedProviderMethodError,Ya as UrlRequiredError,kt as UserRejectedRequestError,Bc as WaitForCallsStatusTimeoutError,Vi as WaitForTransactionReceiptTimeoutError,xs as WebSocketRequestError,Ua as assertCurrentChain,Le as assertRequest,on as assertTransactionEIP1559,uo as assertTransactionEIP2930,fo as assertTransactionLegacy,Pr as blobsToCommitments,Tr as blobsToProofs,np as boolToBytes,ri as boolToHex,rp as bytesToBigInt,sp as bytesToBool,ne as bytesToHex,ap as bytesToNumber,qp as bytesToRlp,op as bytesToString,ao as ccipFetch,ao as ccipRequest,ta as checksumAddress,Ca as commitmentToVersionedHash,Ba as commitmentsToVersionedHashes,g0 as compactSignatureToHex,p0 as compactSignatureToSignature,Ut as concat,ip as concatBytes,ue as concatHex,Rr as createClient,Vu as createNonceManager,i0 as createPublicClient,c0 as createTestClient,sn as createTransport,u0 as createWalletClient,Hl as custom,Ht as decodeAbiParameters,l0 as decodeDeployData,Qs as decodeErrorResult,oa as decodeEventLog,qc as decodeFunctionData,Ge as decodeFunctionResult,cp as defineBlock,up as defineChain,kl as defineKzg,dp as defineTransaction,fp as defineTransactionReceipt,lp as defineTransactionRequest,La as deploylessCallViaBytecodeBytecode,pc as deploylessCallViaFactoryBytecode,pp as domainSeparator,Ye as encodeAbiParameters,tn as encodeDeployData,Fs as encodeErrorResult,ut as encodeEventTopics,oe as encodeFunctionData,_c as encodeFunctionResult,Zl as encodePacked,Ol as erc1155Abi,ql as erc20Abi,_l as erc20Abi_bytes32,Gl as erc4626Abi,Ll as erc721Abi,Jd as ethAddress,ga as etherUnits,Wl as extractChain,Ul as fallback,na as formatBlock,wr as formatEther,te as formatGwei,Ie as formatLog,di as formatTransaction,fi as formatTransactionReceipt,_e as formatTransactionRequest,xa as formatUnits,x0 as fromBlobs,mp as fromBytes,hp as fromHex,lo as fromRlp,Ke as getAbiItem,Ee as getAddress,Qe as getChainContractAddress,jl as getContract,Kl as getContractAddress,Re as getContractError,Bu as getCreate2Address,Cu as getCreateAddress,yp as getEventSelector,bp as getEventSignature,dr as getFunctionSelector,gp as getFunctionSignature,qu as getSerializedTransactionType,Ra as getTransactionType,Pi as getTypesForEIP712Domain,wa as gweiUnits,wp as hashDomain,Or as hashMessage,xp as hashStruct,ua as hashTypedData,j as hexToBigInt,Ei as hexToBool,ce as hexToBytes,m0 as hexToCompactSignature,V as hexToNumber,_p as hexToRlp,h0 as hexToSignature,vp as hexToString,Vl as http,pe as isAddress,Oe as isAddressEqual,zs as isBytes,ho as isErc6492Signature,$u as isHash,G as isHex,ve as keccak256,ti as labelhash,Op as maxInt104,Lp as maxInt112,Gp as maxInt120,jp as maxInt128,Dp as maxInt136,Hp as maxInt144,Up as maxInt152,Vp as maxInt16,Wp as maxInt160,Zp as maxInt168,Kp as maxInt176,Yp as maxInt184,Jp as maxInt192,Xp as maxInt200,Qp as maxInt208,em as maxInt216,tm as maxInt224,nm as maxInt232,rm as maxInt24,sm as maxInt240,am as maxInt248,om as maxInt256,im as maxInt32,cm as maxInt40,um as maxInt48,dm as maxInt56,fm as maxInt64,lm as maxInt72,pm as maxInt8,mm as maxInt80,hm as maxInt88,ym as maxInt96,bm as maxUint104,gm as maxUint112,wm as maxUint120,xm as maxUint128,vm as maxUint136,Em as maxUint144,Pm as maxUint152,Tm as maxUint16,Im as maxUint160,Am as maxUint168,Sm as maxUint176,Cm as maxUint184,Bm as maxUint192,km as maxUint200,$m as maxUint208,Nm as maxUint216,Fm as maxUint224,Rm as maxUint232,Mm as maxUint24,zm as maxUint240,qm as maxUint248,Zt as maxUint256,_m as maxUint32,Om as maxUint40,Lm as maxUint48,Gm as maxUint56,jm as maxUint64,Dm as maxUint72,Hm as maxUint8,Um as maxUint80,Vm as maxUint88,Wm as maxUint96,Zm as minInt104,Km as minInt112,Ym as minInt120,Jm as minInt128,Xm as minInt136,Qm as minInt144,eh as minInt152,th as minInt16,nh as minInt160,rh as minInt168,sh as minInt176,ah as minInt184,oh as minInt192,ih as minInt200,ch as minInt208,uh as minInt216,dh as minInt224,fh as minInt232,lh as minInt24,ph as minInt240,mh as minInt248,hh as minInt256,yh as minInt32,bh as minInt40,gh as minInt48,wh as minInt56,xh as minInt64,vh as minInt72,Eh as minInt8,Ph as minInt80,Th as minInt88,Ih as minInt96,Vn as multicall3Abi,Nt as namehash,s0 as nonceManager,Ep as numberToBytes,M as numberToHex,bu as offchainLookup,so as offchainLookupAbiItem,yu as offchainLookupSignature,Bt as pad,Pp as padBytes,Yr as padHex,aa as parseAbi,Jr as parseAbiItem,Rl as parseAbiParameter,Ml as parseAbiParameters,m0 as parseCompactSignature,t0 as parseErc6492Signature,n0 as parseEther,pr as parseEventLogs,r0 as parseGwei,h0 as parseSignature,_u as parseTransaction,yo as parseUnits,qi as prepareEncodeFunctionData,Nu as presignMessagePrefix,zf as publicActions,Je as recoverAddress,Ru as recoverMessageAddress,Xi as recoverPublicKey,y0 as recoverTransactionAddress,Mu as recoverTypedDataAddress,Yl as ripemd160,Dl as rpcSchema,Tp as rpcTransactionType,cn as serializeAccessList,g0 as serializeCompactSignature,zu as serializeErc6492Signature,Tf as serializeSignature,vu as serializeTransaction,Ii as serializeTypedData,Ip as setErrorConfig,v0 as setupKzg,Ci as sha256,Fc as shouldThrow,w0 as sidecarsToVersionedHashes,b0 as signatureToCompactSignature,Tf as signatureToHex,ft as size,_t as slice,Ap as sliceBytes,da as sliceHex,Ct as stringToBytes,ot as stringToHex,X as stringify,pl as testActions,Ar as toBlobSidecars,dc as toBlobs,Te as toBytes,Sp as toEventHash,Cp as toEventSelector,Bp as toEventSignature,kp as toFunctionHash,dr as toFunctionSelector,$p as toFunctionSignature,_ as toHex,Fu as toPrefixedMessage,Ae as toRlp,Np as transactionType,Ze as trim,$s as universalSignatureValidatorAbi,mc as universalSignatureValidatorByteCode,Ti as validateTypedData,Jl as verifyHash,Xl as verifyMessage,Ql as verifyTypedData,Bl as walletActions,d0 as webSocket,zl as weiUnits,Ac as withCache,Yn as withRetry,Ja as withTimeout,fe as zeroAddress,e0 as zeroHash};
