import{aC as I,aD as y,aE as a,aF as m,aG as r,aH as z,aI as A,aJ as v,aK as B,aL as U,aM as W,aN as C,aO as T,aP as O,aQ as P,aR as b,aS as D,aT as F,aU as H,aV as M,aW as q}from"./index-nH9aK6xx.js";function E(e){var i,s,d,u,x,h,f,k,w,j,S;const o=e.size==="compact",{initialScreen:t,screen:n}=I(),[g,l]=y.useState(!1),L=n===e.wallet&&t===e.wallet,c=L&&!e.isLinking?void 0:e.goBack;return a.jsxs(m,{fullHeight:!0,flex:"column",p:"lg",animate:"fadein",style:{minHeight:"250px"},children:[o&&(L?a.jsxs(a.Fragment,{children:[a.jsx(r,{onBack:c,leftAligned:!e.isLinking,title:a.jsxs(a.Fragment,{children:[(i=e.meta)!=null&&i.titleIconUrl?a.jsx(z,{src:(s=e.meta)==null?void 0:s.titleIconUrl,width:A.md,height:A.md,client:e.client}):null,a.jsx(v,{children:((d=e.meta)==null?void 0:d.title)??e.inAppWalletLocale.emailLoginScreen.title})]})}),a.jsx(B,{y:"lg"})]}):a.jsx(r,{onBack:c,title:e.inAppWalletLocale.signIn})),a.jsx(m,{expand:!0,flex:"column",center:"y",p:o?void 0:"lg",children:a.jsx(U,{...e,locale:e.inAppWalletLocale,disabled:((u=e.meta)==null?void 0:u.requireApproval)&&!g})}),o&&(((x=e.meta)==null?void 0:x.showThirdwebBranding)!==!1||((h=e.meta)==null?void 0:h.termsOfServiceUrl)||((f=e.meta)==null?void 0:f.privacyPolicyUrl))&&a.jsx(B,{y:"xl"}),a.jsxs(m,{flex:"column",gap:"lg",children:[a.jsx(W,{termsOfServiceUrl:(k=e.meta)==null?void 0:k.termsOfServiceUrl,privacyPolicyUrl:(w=e.meta)==null?void 0:w.privacyPolicyUrl,locale:e.connectLocale.agreement,requireApproval:(j=e.meta)==null?void 0:j.requireApproval,onApprove:()=>{l(!g)},isApproved:g}),((S=e.meta)==null?void 0:S.showThirdwebBranding)!==!1&&a.jsx(C,{})]})]})}function K(e){const o=T(),t=O(),n=o,g=e.connectLocale.id,l=P(g),{initialScreen:L}=I();if(!l)return a.jsx(b,{});const c=()=>{var d;L===e.wallet?t({}):((d=e.goBack)==null||d.call(e),t({}))},i=()=>{e.done(),t({})},s=n!=null&&n.emailLogin?{email:n.emailLogin}:n!=null&&n.phoneLogin?{phone:n.phoneLogin}:void 0;return s?a.jsx(D,{userInfo:s,locale:l,done:i,goBack:c,wallet:e.wallet,chain:e.chain,client:e.client,size:e.size,isLinking:e.isLinking}):n!=null&&n.passkeyLogin?a.jsx(F,{locale:e.connectLocale,wallet:e.wallet,done:i,onBack:c,chain:e.chain,client:e.client,size:e.size,isLinking:e.isLinking}):n!=null&&n.walletLogin?a.jsx(H,{meta:e.meta,inAppLocale:l,walletConnect:e.walletConnect,wallet:e.wallet,client:e.client,size:e.size,chain:e.chain,done:i,onBack:c||(()=>t({})),locale:e.connectLocale,isLinking:n.walletLogin.linking}):n!=null&&n.socialLogin?a.jsx(M,{socialAuth:n.socialLogin.type,locale:l,done:i,goBack:c,wallet:e.wallet,state:n,chain:e.chain,client:e.client,size:e.size,connectLocale:e.connectLocale,isLinking:e.isLinking}):n!=null&&n.guestLogin?a.jsx(q,{locale:l,done:i,goBack:c,wallet:e.wallet,state:n,client:e.client,size:e.size,connectLocale:e.connectLocale}):a.jsx(E,{select:()=>{},connectLocale:e.connectLocale,inAppWalletLocale:l,done:i,goBack:e.goBack,wallet:e.wallet,client:e.client,meta:e.meta,size:e.size,chain:e.chain,isLinking:e.isLinking})}export{K as default};
