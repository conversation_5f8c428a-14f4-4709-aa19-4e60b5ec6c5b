import{aG as c,aT as i,aU as s,dz as d,aI as t,dA as o,aV as u,aP as m}from"./index-Dtah4Gl2.js";function S(e){const{screen:a}=c(),n=i(),l=s(e.connectLocale.id);return e.size==="wide"||a!==d.main&&e.size==="compact"?t.jsx(o,{wallet:e.wallet,selectWallet:()=>{n({}),e.select()},client:e.client,connectLocale:e.connectLocale,recommendedWallets:e.recommendedWallets,isActive:a===e.wallet,badge:void 0}):l?t.jsx(m,{disabled:e.disabled,locale:l,wallet:e.wallet,done:e.done,select:e.select,goBack:e.goBack,chain:e.chain,client:e.client,size:e.size}):t.jsx(u,{height:"195px"})}export{S as default};
