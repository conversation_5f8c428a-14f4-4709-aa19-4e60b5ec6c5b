import{bC as H,aX as P,e4 as C,e5 as U,ak as N,e6 as O,e7 as R,e8 as k,e9 as L,dT as j,dJ as z,r as w,z as g,b as W,i as v,F as h,J as G,ea as J,eb as Z,a1 as q,ec as K,ed as X,ee as S,ef as Y,y as b,eg as T,eh as Q,ei as e0,ej as t0,dM as a0,A as x,n as D}from"./index-nH9aK6xx.js";import{concatHex as r0}from"./concat-hex-BWCEa--n.js";import{a as n0}from"./Signature-BvxbNUPG.js";import{p as I}from"./index-DJaQ5E5p.js";import"./send-eip712-transaction-DQ1sGspl.js";import"./eth_sendRawTransaction-DPdnXbFR.js";import"./sha256-SnVv3535.js";function s0(e){for(const a of e)if(typeof a!="string")return!1;return!0}function i0(e){return s0(e)?H(e):e}function o0(e,a){var n;const{bytecode:t,args:r}=a;return P(t,(n=e.inputs)!=null&&n.length&&(r!=null&&r.length)?C(e.inputs,r):"0x")}function c0(e){const a=e.find(t=>t.type==="constructor");if(!a)throw new U({name:"constructor"});return a}function d0(e,a){const t={to:a};switch(t.to){case"number":return b0(e,t);case"bigint":return f0(e,t);case"boolean":return u0(e,t);case"string":return l0(e,t);default:return N(e,t)}}function f0(e,a={}){return O(e,a)}function u0(e,a={}){return R(e,a)}function b0(e,a={}){return k(e,a)}function l0(e,a={}){return L(e,a)}const y0="0x6492649264926492649264926492649264926492649264926492649264926492",p0="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",g0=[{inputs:[{name:"_signer",type:"address"},{name:"_hash",type:"bytes32"},{name:"_signature",type:"bytes"}],stateMutability:"nonpayable",type:"constructor"},{inputs:[{name:"_signer",type:"address"},{name:"_hash",type:"bytes32"},{name:"_signature",type:"bytes"}],outputs:[{type:"bool"}],stateMutability:"nonpayable",type:"function",name:"isValidSig"}];function h0(e){if(j(e,-32)!==y0)throw new w0(e)}function m0(e){try{return h0(e),!0}catch{return!1}}class w0 extends z{constructor(a){super(`Value \`${a}\` is an invalid ERC-6492 wrapped signature.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"WrappedSignature.InvalidWrappedSignatureError"})}}const v0="0x1626ba7e",A0=[{type:"bytes32",name:"hash"},{type:"bytes",name:"signature"}],E0=[{type:"bytes4"}];async function S0(e){return w({contract:e.contract,method:[v0,A0,E0],params:[e.hash,e.signature]})}const T0="0x6492649264926492649264926492649264926492649264926492649264926492";function A({address:e,data:a,signature:t}){return r0([g([{type:"address"},{type:"bytes"},{type:"bytes"}],[e,a,t]),T0])}const x0="******************************************";async function V({hash:e,signature:a,address:t,client:r,chain:n,accountFactory:s}){const i=(()=>{if(W(a))return a;if(typeof a=="object"&&"r"in a&&"s"in a)return n0(a);if(a instanceof Uint8Array)return d0(a,"hex");throw new Error(`Invalid signature type for signature ${a}: ${typeof a}`)})();if(await v(h({address:t,client:r,chain:n}))&&await m({hash:e,signature:i,contract:h({chain:n,address:t,client:r})}).catch(u=>(console.error("Error verifying EIP-1271 signature",u),!1)))return!0;const p=await(async()=>!s||m0(i)?i:A({address:s.address,data:s.verificationCalldata,signature:i}))();let d;const c=await G(n),l=i0(g0);if(c)d={to:x0,data:J(Z(l,"isValidSig"),[t,e,p])};else{const f=c0(l);d={data:o0(f,{args:[t,e,p],bytecode:p0})}}const y=q({chain:n,client:r});try{const f=await K(y,d);return X(f)}catch{return!!await m({hash:e,signature:i,contract:h({chain:n,address:t,client:r})}).catch(u=>(console.error("Error verifying EIP-1271 signature",u),!1))}}const D0="0x1626ba7e";async function m({hash:e,signature:a,contract:t}){try{return await S0({hash:e,signature:a,contract:t})===D0}catch(r){return console.error("Error verifying EIP-1271 signature",r),!1}}const I0=`Ethereum Signed Message:
`;function V0(e,a){const t=typeof e=="string"?S(e):e.raw instanceof Uint8Array?e.raw:Y(e.raw),r=S(`${I0}${t.length}`);return b(T(r,t),a)}function M0(e){const{domain:a={},message:t,primaryType:r}=e,n={EIP712Domain:Q(a),...e.types};e0({domain:a,message:t,primaryType:r,types:n});const s=["0x1901"];if(a&&s.push(t0({domain:a,types:n})),r!=="EIP712Domain"){const i=(()=>{const o=M({data:t,primaryType:r,types:n});return b(o)})();s.push(i)}return b(T(...s.map(i=>a0(i))))}function M({data:e,primaryType:a,types:t}){const r=[{type:"bytes32"}],n=[_0({primaryType:a,types:t})];if(!t[a])throw new Error("Invalid types");for(const s of t[a]){const[i,o]=B({types:t,name:s.name,type:s.type,value:e[s.name]});r.push(i),n.push(o)}return g(r,n)}function _0({primaryType:e,types:a}){const t=x(B0({primaryType:e,types:a}));return b(t)}function B0({primaryType:e,types:a}){let t="";const r=_({primaryType:e,types:a});r.delete(e);const n=[e,...Array.from(r).sort()];for(const s of n){if(!a[s])throw new Error("Invalid types");t+=`${s}(${a[s].map(({name:i,type:o})=>`${o} ${i}`).join(",")})`}return t}function _({primaryType:e,types:a},t=new Set){const r=e.match(/^\w*/u),n=r==null?void 0:r[0];if(t.has(n)||a[n]===void 0)return t;t.add(n);for(const s of a[n])_({primaryType:s.type,types:a},t);return t}function B({types:e,name:a,type:t,value:r}){if(e[t]!==void 0)return[{type:"bytes32"},b(M({data:r,primaryType:t,types:e}))];if(t==="bytes")return r=`0x${(r.length%2?"0":"")+r.slice(2)}`,[{type:"bytes32"},b(r)];if(t==="string")return[{type:"bytes32"},b(x(r))];if(t.lastIndexOf("]")===t.length-1){const n=t.slice(0,t.lastIndexOf("[")),s=r.map(i=>B({name:a,type:n,types:e,value:i}));return[{type:"bytes32"},b(g(s.map(([i])=>i),s.map(([,i])=>i)))]}return[{type:t},r]}async function R0({accountContract:e,factoryContract:a,options:t,message:r}){var p,d;const n=V0(r),s=await $({factoryContract:a,accountContract:e,originalMsgHash:n});let i;if(s){const c=g([{type:"bytes32"}],[n]);i=await t.personalAccount.signTypedData({domain:{name:"Account",version:"1",chainId:t.chain.id,verifyingContract:e.address},primaryType:"AccountMessage",types:{AccountMessage:[{name:"message",type:"bytes"}]},message:{message:c}})}else i=await t.personalAccount.signMessage({message:r});if(await v(e)){if(await m({hash:n,signature:i,contract:e}))return i;throw new Error("Failed to verify signature")}else{const c=I({factoryContract:a,adminAddress:t.personalAccount.address,accountSalt:(p=t.overrides)==null?void 0:p.accountSalt,createAccountOverride:(d=t.overrides)==null?void 0:d.createAccount});if(!c)throw new Error("Create account override not provided");const l=await D(c),y=A({address:a.address,data:l,signature:i});if(await V({hash:n,signature:y,address:e.address,chain:e.chain,client:e.client}))return y;throw new Error("Unable to verify ERC-6492 signature after signing.")}}async function k0({accountContract:e,factoryContract:a,options:t,typedData:r}){var d,c,l,y,f;if(((c=(d=r.domain)==null?void 0:d.verifyingContract)==null?void 0:c.toLowerCase())===((l=e.address)==null?void 0:l.toLowerCase()))return t.personalAccount.signTypedData(r);const s=M0(r),i=await $({factoryContract:a,accountContract:e,originalMsgHash:s});let o;if(i){const u=g([{type:"bytes32"}],[s]);o=await t.personalAccount.signTypedData({domain:{name:"Account",version:"1",chainId:t.chain.id,verifyingContract:e.address},primaryType:"AccountMessage",types:{AccountMessage:[{name:"message",type:"bytes"}]},message:{message:u}})}else o=await t.personalAccount.signTypedData(r);if(await v(e)){if(await m({hash:s,signature:o,contract:e}))return o;throw new Error("Failed to verify signature")}else{const u=I({factoryContract:a,adminAddress:t.personalAccount.address,accountSalt:(y=t.overrides)==null?void 0:y.accountSalt,createAccountOverride:(f=t.overrides)==null?void 0:f.createAccount});if(!u)throw new Error("Create account override not provided");const F=await D(u),E=A({address:a.address,data:F,signature:o});if(await V({hash:s,signature:E,address:e.address,chain:e.chain,client:e.client}))return E;throw new Error("Unable to verify signature on smart account, please make sure the admin wallet has permissions and the signature is valid.")}}async function $({factoryContract:e,accountContract:a,originalMsgHash:t}){try{const r=await w({contract:e,method:"function accountImplementation() public view returns (address)"});return await w({contract:h({address:r,chain:a.chain,client:a.client}),method:"function getMessageHash(bytes32 _hash) public view returns (bytes32)",params:[t]}).then(s=>s!=="0x").catch(()=>!1)}catch{return!1}}export{R0 as smartAccountSignMessage,k0 as smartAccountSignTypedData};
