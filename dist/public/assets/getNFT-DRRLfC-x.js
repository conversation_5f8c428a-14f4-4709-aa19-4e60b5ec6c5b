import{r as a,dC as e,dD as d}from"./index-Dtah4Gl2.js";import{f as o}from"./fetchTokenMetadata-CLF9kbPt.js";import{totalSupply as u}from"./totalSupply-RPMBI3pG.js";import"./detectExtension-EOVHjH6S.js";const i="0x0e89341c",I=[{type:"uint256",name:"tokenId"}],k=[{type:"string"}];async function m(t){return a({contract:t.contract,method:[i,I,k],params:[t.tokenId]})}async function F(t){const{useIndexer:c=!0}=t;if(c)try{return await f(t)}catch{return await r(t)}return await r(t)}async function f(t){const c=await e({client:t.contract.client,chain:t.contract.chain,contractAddress:t.contract.address,tokenId:t.tokenId});return c||r(t)}async function r(t){const[c,n]=await Promise.all([m({contract:t.contract,tokenId:t.tokenId}),u({contract:t.contract,id:t.tokenId}).catch(()=>0n)]);return d(await o({client:t.contract.client,tokenId:t.tokenId,tokenUri:c}).catch(()=>({id:t.tokenId,type:"ERC1155",uri:c})),{tokenId:t.tokenId,tokenUri:c,type:"ERC1155",owner:null,supply:n,tokenAddress:t.contract.address,chainId:t.contract.chain.id})}export{F as getNFT};
