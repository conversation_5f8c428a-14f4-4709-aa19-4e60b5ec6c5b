import { useState, useEffect } from "react";
import { useActiveAccount, useActiveWallet } from "thirdweb/react";
import {
  MessageCircleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ExternalLinkIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  AlertCircleIcon,
  RefreshCwIcon,
} from "lucide-react";
import { useChatStore } from "@/store/chatStore";
import { apiRequest } from "@/lib/queryClient";
import { getExplorerUrl } from "@/lib/chainConfig";
import { useTransactionHistory } from "@/hooks/useTransactionHistory";
import { Button } from "@/components/ui/button";
import {
  type ActivityItem,
  transformTransactionToActivity,
  formatTimeAgo,
  getChainName,
  sortActivitiesByTime,
} from "@/lib/transactionUtils";

const RecentActivity = () => {
  const { chats } = useChatStore();
  const account = useActiveAccount();
  const address = account?.address;
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [isLoadingChats, setIsLoadingChats] = useState(false);

  // Use the transaction history hook
  const {
    transactions,
    isLoading: isLoadingTransactions,
    error: transactionError,
    refetch: refetchTransactions,
  } = useTransactionHistory();

  // Generate activity items from chats and blockchain interactions
  useEffect(() => {
    const generateActivities = async () => {
      setIsLoadingChats(true);
      try {
        const activityItems: ActivityItem[] = [];

        // Add recent chat activities
        const recentChats = chats.slice(0, 5); // Last 5 chats
        for (const chat of recentChats) {
          // Get messages for this chat to determine activity type
          try {
            const response = await apiRequest(
              "GET",
              `/api/chats/${chat.id}/messages`
            );
            const messages = await response.json();

            if (messages.length > 0) {
              const lastMessage = messages[messages.length - 1];
              activityItems.push({
                id: `chat-${chat.id}`,
                type: "chat",
                title: chat.title,
                description: `Last message: ${lastMessage.content.substring(
                  0,
                  50
                )}...`,
                timestamp: new Date(lastMessage.timestamp),
                chatId: chat.id,
              });
            }
          } catch (error) {
            // Skip if can't fetch messages
          }
        }

        // Transform real transaction data into activity items
        if (address && transactions.length > 0) {
          const transactionActivities = transactions
            .slice(0, 10) // Limit to 10 most recent transactions
            .map((tx) => transformTransactionToActivity(tx, address));

          activityItems.push(...transactionActivities);
        }

        // Sort by timestamp (most recent first)
        const sortedActivities = sortActivitiesByTime(activityItems);

        setActivities(sortedActivities.slice(0, 15)); // Show last 15 activities
      } catch (error) {
        console.error("Error generating activities:", error);
      } finally {
        setIsLoadingChats(false);
      }
    };

    generateActivities();
  }, [chats, address, transactions]);

  const getActivityIcon = (activity: ActivityItem) => {
    switch (activity.type) {
      case "chat":
        return <MessageCircleIcon className="h-4 w-4" />;
      case "transaction":
        return activity.description.toLowerCase().includes("sent") ||
          activity.description.toLowerCase().includes("transfer") ? (
          <ArrowUpIcon className="h-4 w-4" />
        ) : (
          <ArrowDownIcon className="h-4 w-4" />
        );
      case "contract_interaction":
        return <ExternalLinkIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case "success":
        return <CheckCircleIcon className="h-3 w-3 text-green-500" />;
      case "failed":
        return <XCircleIcon className="h-3 w-3 text-red-500" />;
      case "pending":
        return <AlertCircleIcon className="h-3 w-3 text-yellow-500" />;
      default:
        return null;
    }
  };

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - timestamp.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  const isLoading = isLoadingChats || isLoadingTransactions;

  // Show error state for transaction errors
  if (transactionError && !isLoading && activities.length === 0) {
    return (
      <div className="text-center py-8">
        <XCircleIcon className="h-8 w-8 text-destructive mx-auto mb-2" />
        <p className="text-sm text-muted-foreground mb-2">
          Failed to load transactions
        </p>
        <p className="text-xs text-muted-foreground mb-4">{transactionError}</p>
        <Button
          variant="outline"
          size="sm"
          onClick={refetchTransactions}
          className="gap-2"
        >
          <RefreshCwIcon className="h-4 w-4" />
          Retry
        </Button>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-muted rounded-md"></div>
              <div className="flex-1 space-y-2">
                <div className="h-3 bg-muted rounded w-3/4"></div>
                <div className="h-2 bg-muted rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (activities.length === 0) {
    return (
      <div className="text-center py-8">
        <ClockIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <p className="text-sm text-muted-foreground">No recent activity</p>
        <p className="text-xs text-muted-foreground mt-1">
          Start chatting or connect your wallet to see activity
        </p>
        {address && (
          <Button
            variant="outline"
            size="sm"
            onClick={refetchTransactions}
            className="gap-2 mt-4"
          >
            <RefreshCwIcon className="h-4 w-4" />
            Refresh
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {/* Header with refresh button */}
      <div className="flex items-center justify-between px-3 py-2">
        <span className="text-xs font-medium text-muted-foreground">
          Recent Activity
        </span>
        <Button
          variant="ghost"
          size="sm"
          onClick={refetchTransactions}
          disabled={isLoading}
          className="h-6 w-6 p-0"
        >
          <RefreshCwIcon
            className={`h-3 w-3 ${isLoading ? "animate-spin" : ""}`}
          />
        </Button>
      </div>

      {activities.map((activity) => (
        <div
          key={activity.id}
          className="flex items-start gap-3 p-3 rounded-md hover:bg-background/80 transition-colors cursor-pointer"
          onClick={() => {
            if (activity.type === "chat" && activity.chatId) {
              // Handle chat selection - this would need to be passed as a prop
              console.log("Select chat:", activity.chatId);
            } else if (activity.txHash) {
              // Open transaction in explorer
              const chainId = parseInt(activity.chainId || "1");
              getExplorerUrl(chainId, "tx", activity.txHash)
                .then((explorerUrl) => {
                  window.open(explorerUrl, "_blank");
                })
                .catch((error) => {
                  console.error("Failed to get explorer URL:", error);
                });
            }
          }}
        >
          <div className="flex-shrink-0 w-10 h-10 bg-muted/50 rounded-md flex items-center justify-center text-muted-foreground">
            {getActivityIcon(activity)}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between gap-2">
              <p className="text-sm font-medium text-foreground truncate">
                {activity.title}
              </p>
              <div className="flex items-center gap-1">
                {getStatusIcon(activity.status)}
                <span className="text-xs text-muted-foreground whitespace-nowrap">
                  {formatTimeAgo(activity.timestamp)}
                </span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground truncate mt-1">
              {activity.description}
            </p>
            {activity.chainId && (
              <div className="flex items-center gap-1 mt-1">
                <span className="text-xs bg-muted/50 px-2 py-0.5 rounded text-muted-foreground">
                  {getChainName(parseInt(activity.chainId))}
                </span>
                {activity.value && parseFloat(activity.value) > 0 && (
                  <span className="text-xs bg-primary/10 px-2 py-0.5 rounded text-primary">
                    {activity.type === "transaction" ? "ETH" : "Token"}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default RecentActivity;
