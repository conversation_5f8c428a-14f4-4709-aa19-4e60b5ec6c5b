import { useState, useEffect, useCallback } from "react";
import { useActiveAccount, useActiveWallet } from "thirdweb/react";
import { getTransactionStore, getPastTransactions } from "thirdweb/transaction";
import { getTransactions } from "thirdweb/insight";
import { client, chains } from "@/lib/thirdweb";
import type { Chain } from "thirdweb/chains";

export interface TransactionData {
  hash: string;
  chainId: number;
  status: "pending" | "success" | "failed";
  timestamp: Date;
  from: string;
  to: string;
  value: string;
  gasUsed?: string;
  gasPrice?: string;
  blockNumber?: number;
  methodId?: string;
  functionName?: string;
  tokenTransfers?: Array<{
    from: string;
    to: string;
    value: string;
    tokenAddress: string;
    tokenSymbol?: string;
    tokenName?: string;
  }>;
}

export interface UseTransactionHistoryReturn {
  transactions: TransactionData[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  hasMore: boolean;
  loadMore: () => Promise<void>;
}

export function useTransactionHistory(): UseTransactionHistoryReturn {
  const account = useActiveAccount();
  const wallet = useActiveWallet();
  const [transactions, setTransactions] = useState<TransactionData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);

  const fetchTransactions = useCallback(async (reset = false) => {
    if (!account?.address) {
      setTransactions([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Get transactions from thirdweb Insight API
      const insightTransactions = await getTransactions({
        client,
        walletAddress: account.address,
        chains: chains.slice(0, 5), // Limit to first 5 chains to avoid rate limits
        queryOptions: {
          limit: 20,
          offset: reset ? 0 : offset,
        },
      });

      // Transform insight transactions to our format
      const transformedTransactions: TransactionData[] = insightTransactions.map((tx) => ({
        hash: tx.hash,
        chainId: tx.chain_id,
        status: tx.status === 1 ? "success" : "failed",
        timestamp: new Date(tx.block_timestamp * 1000),
        from: tx.from_address,
        to: tx.to_address,
        value: tx.value,
        gasUsed: tx.gas_used?.toString(),
        gasPrice: tx.gas_price?.toString(),
        blockNumber: tx.block_number,
        methodId: tx.input?.slice(0, 10),
        functionName: getFunctionName(tx.input),
        tokenTransfers: tx.token_transfers?.map((transfer: any) => ({
          from: transfer.from_address,
          to: transfer.to_address,
          value: transfer.value,
          tokenAddress: transfer.contract_address,
          tokenSymbol: transfer.token_symbol,
          tokenName: transfer.token_name,
        })),
      }));

      if (reset) {
        setTransactions(transformedTransactions);
        setOffset(20);
      } else {
        setTransactions(prev => [...prev, ...transformedTransactions]);
        setOffset(prev => prev + 20);
      }

      setHasMore(transformedTransactions.length === 20);
    } catch (err) {
      console.error("Failed to fetch transactions:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch transactions");
      
      // Fallback to local transaction store if Insight API fails
      try {
        if (wallet) {
          const store = getTransactionStore(account.address);
          const localTransactions = store.getValue();
          
          const transformedLocal: TransactionData[] = localTransactions.map((tx) => ({
            hash: tx.transactionHash,
            chainId: tx.chainId,
            status: tx.receipt?.status || "pending",
            timestamp: new Date(), // We don't have timestamp in local store
            from: account.address,
            to: tx.receipt?.to || "",
            value: "0", // We don't have value in local store
          }));

          if (reset) {
            setTransactions(transformedLocal);
          } else {
            setTransactions(prev => [...prev, ...transformedLocal]);
          }
        }
      } catch (localErr) {
        console.error("Failed to fetch local transactions:", localErr);
      }
    } finally {
      setIsLoading(false);
    }
  }, [account?.address, wallet, offset]);

  const refetch = useCallback(async () => {
    setOffset(0);
    await fetchTransactions(true);
  }, [fetchTransactions]);

  const loadMore = useCallback(async () => {
    if (!isLoading && hasMore) {
      await fetchTransactions(false);
    }
  }, [fetchTransactions, isLoading, hasMore]);

  // Initial fetch when account changes
  useEffect(() => {
    if (account?.address) {
      refetch();
    } else {
      setTransactions([]);
      setError(null);
    }
  }, [account?.address, refetch]);

  // Subscribe to real-time transaction updates
  useEffect(() => {
    if (!account?.address || !wallet) return;

    const store = getTransactionStore(account.address);
    const unsubscribe = store.subscribe((newTransactions) => {
      // Add new transactions to the beginning of the list
      const newTxs: TransactionData[] = newTransactions
        .filter(tx => !transactions.some(existing => existing.hash === tx.transactionHash))
        .map(tx => ({
          hash: tx.transactionHash,
          chainId: tx.chainId,
          status: tx.receipt?.status || "pending",
          timestamp: new Date(),
          from: account.address,
          to: tx.receipt?.to || "",
          value: "0",
        }));

      if (newTxs.length > 0) {
        setTransactions(prev => [...newTxs, ...prev]);
      }
    });

    return unsubscribe;
  }, [account?.address, wallet, transactions]);

  return {
    transactions,
    isLoading,
    error,
    refetch,
    hasMore,
    loadMore,
  };
}

// Helper function to extract function name from transaction input
function getFunctionName(input?: string): string | undefined {
  if (!input || input.length < 10) return undefined;
  
  const methodId = input.slice(0, 10);
  
  // Common method IDs and their function names
  const knownMethods: Record<string, string> = {
    "0xa9059cbb": "transfer",
    "0x23b872dd": "transferFrom",
    "0x095ea7b3": "approve",
    "0x40c10f19": "mint",
    "0x42842e0e": "safeTransferFrom",
    "0xa22cb465": "setApprovalForAll",
    "0x7ff36ab5": "swapExactETHForTokens",
    "0x38ed1739": "swapExactTokensForTokens",
    "0x8803dbee": "swapTokensForExactTokens",
    "0x02751cec": "removeLiquidity",
    "0xf305d719": "addLiquidityETH",
    "0xe8e33700": "addLiquidity",
  };

  return knownMethods[methodId];
}
