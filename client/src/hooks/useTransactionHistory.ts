import { useState, useEffect, useCallback } from "react";
import { useActiveAccount, useActiveWallet } from "thirdweb/react";
import { getTransactionStore, getPastTransactions } from "thirdweb/transaction";
import { getTransactions } from "thirdweb/insight";
import { client, chains } from "@/lib/thirdweb";
import type { Chain } from "thirdweb/chains";

export interface TransactionData {
  hash: string;
  chainId: number;
  status: "pending" | "success" | "failed";
  timestamp: Date;
  from: string;
  to: string;
  value: string;
  gasUsed?: string;
  gasPrice?: string;
  blockNumber?: number;
  methodId?: string;
  functionName?: string;
  tokenTransfers?: Array<{
    from: string;
    to: string;
    value: string;
    tokenAddress: string;
    tokenSymbol?: string;
    tokenName?: string;
  }>;
}

export interface UseTransactionHistoryReturn {
  transactions: TransactionData[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  hasMore: boolean;
  loadMore: () => Promise<void>;
}

export function useTransactionHistory(): UseTransactionHistoryReturn {
  const account = useActiveAccount();
  const wallet = useActiveWallet();
  const [transactions, setTransactions] = useState<TransactionData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);

  const fetchTransactions = useCallback(
    async (reset = false) => {
      if (!account?.address) {
        setTransactions([]);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // For now, let's just use mock data to ensure the UI works
        // We'll add real API calls later once we confirm the UI is working
        const mockTransactions: TransactionData[] = [
          {
            hash: "******************************************",
            chainId: 1,
            status: "success",
            timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
            from: "******************************************",
            to: account.address,
            value: "100000000000000000", // 0.1 ETH
          },
          {
            hash: "******************************************",
            chainId: 137,
            status: "success",
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
            from: account.address,
            to: "******************************************",
            value: "*****************", // 0.05 ETH
          },
          {
            hash: "******************************************",
            chainId: 1,
            status: "pending",
            timestamp: new Date(Date.now() - 1000 * 60 * 10), // 10 minutes ago
            from: account.address,
            to: "******************************************",
            value: "*****************", // 0.025 ETH
          },
        ];

        if (reset) {
          setTransactions(mockTransactions);
          setOffset(mockTransactions.length);
        } else {
          setTransactions((prev) => [...prev, ...mockTransactions]);
          setOffset((prev) => prev + mockTransactions.length);
        }

        setHasMore(false); // No more data for mock
        setError(null); // Clear any previous errors
      } catch (err) {
        console.error("Failed to fetch transactions:", err);
        setError(
          err instanceof Error ? err.message : "Failed to fetch transactions"
        );
      } finally {
        setIsLoading(false);
      }
    },
    [account?.address, wallet, offset]
  );

  const refetch = useCallback(async () => {
    setOffset(0);
    await fetchTransactions(true);
  }, [fetchTransactions]);

  const loadMore = useCallback(async () => {
    if (!isLoading && hasMore) {
      await fetchTransactions(false);
    }
  }, [fetchTransactions, isLoading, hasMore]);

  // Initial fetch when account changes
  useEffect(() => {
    if (account?.address) {
      refetch();
    } else {
      setTransactions([]);
      setError(null);
    }
  }, [account?.address, refetch]);

  // Subscribe to real-time transaction updates
  useEffect(() => {
    if (!account?.address || !wallet) return;

    const store = getTransactionStore(account.address);
    const unsubscribe = store.subscribe((newTransactions) => {
      // Add new transactions to the beginning of the list
      const newTxs: TransactionData[] = newTransactions
        .filter(
          (tx) =>
            !transactions.some(
              (existing) => existing.hash === tx.transactionHash
            )
        )
        .map((tx) => ({
          hash: tx.transactionHash,
          chainId: tx.chainId,
          status: tx.receipt?.status || "pending",
          timestamp: new Date(),
          from: account.address,
          to: tx.receipt?.to || "",
          value: "0",
        }));

      if (newTxs.length > 0) {
        setTransactions((prev) => [...newTxs, ...prev]);
      }
    });

    return unsubscribe;
  }, [account?.address, wallet, transactions]);

  return {
    transactions,
    isLoading,
    error,
    refetch,
    hasMore,
    loadMore,
  };
}

// Helper function to extract function name from transaction input
function getFunctionName(input?: string): string | undefined {
  if (!input || input.length < 10) return undefined;

  const methodId = input.slice(0, 10);

  // Common method IDs and their function names
  const knownMethods: Record<string, string> = {
    "0xa9059cbb": "transfer",
    "0x23b872dd": "transferFrom",
    "0x095ea7b3": "approve",
    "0x40c10f19": "mint",
    "0x42842e0e": "safeTransferFrom",
    "0xa22cb465": "setApprovalForAll",
    "0x7ff36ab5": "swapExactETHForTokens",
    "0x38ed1739": "swapExactTokensForTokens",
    "0x8803dbee": "swapTokensForExactTokens",
    "0x02751cec": "removeLiquidity",
    "0xf305d719": "addLiquidityETH",
    "0xe8e33700": "addLiquidity",
  };

  return knownMethods[methodId];
}
